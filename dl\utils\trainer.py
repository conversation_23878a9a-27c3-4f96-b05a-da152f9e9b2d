"""
模型训练器
"""
import os
import time
import torch
import torch.nn as nn
from torch.optim import <PERSON>
from torch.optim.lr_scheduler import ReduceLROnPlateau
import numpy as np


class Trainer:
    """模型训练器类"""
    
    def __init__(self, model, device, save_path="checkpoints"):
        self.model = model
        self.device = device
        self.save_path = save_path
        
        # 创建保存目录
        os.makedirs(save_path, exist_ok=True)
        
        # 初始化训练历史
        self.train_history = {
            'loss': [],
            'accuracy': [],
            'val_loss': [],
            'val_accuracy': []
        }
        
        self.best_val_acc = 0.0
        self.patience_counter = 0
        
    def train_epoch(self, train_loader, criterion, optimizer):
        """训练一个epoch"""
        self.model.train()
        running_loss = 0.0
        correct = 0
        total = 0
        
        for batch_idx, (data, target) in enumerate(train_loader):
            data, target = data.to(self.device), target.to(self.device)
            
            # 前向传播
            optimizer.zero_grad()
            output = self.model(data)
            loss = criterion(output, target)
            
            # 反向传播
            loss.backward()
            optimizer.step()
            
            # 统计
            running_loss += loss.item()
            _, predicted = torch.max(output.data, 1)
            total += target.size(0)
            correct += (predicted == target).sum().item()
            
            # 打印进度
            if batch_idx % 100 == 0:
                print(f'Batch {batch_idx}/{len(train_loader)}, '
                      f'Loss: {loss.item():.4f}, '
                      f'Acc: {100.*correct/total:.2f}%')
        
        epoch_loss = running_loss / len(train_loader)
        epoch_acc = 100. * correct / total
        
        return epoch_loss, epoch_acc
    
    def validate(self, val_loader, criterion):
        """验证模型"""
        self.model.eval()
        val_loss = 0.0
        correct = 0
        total = 0
        
        with torch.no_grad():
            for data, target in val_loader:
                data, target = data.to(self.device), target.to(self.device)
                output = self.model(data)
                val_loss += criterion(output, target).item()
                
                _, predicted = torch.max(output.data, 1)
                total += target.size(0)
                correct += (predicted == target).sum().item()
        
        val_loss /= len(val_loader)
        val_acc = 100. * correct / total
        
        return val_loss, val_acc
    
    def train(self, train_loader, val_loader, epochs=100, lr=0.001, 
              weight_decay=1e-4, patience=10):
        """完整的训练流程"""
        
        # 初始化优化器和损失函数
        criterion = nn.CrossEntropyLoss()
        optimizer = Adam(self.model.parameters(), lr=lr, weight_decay=weight_decay)
        scheduler = ReduceLROnPlateau(optimizer, mode='max', factor=0.5, 
                                    patience=5, verbose=True)
        
        print(f"开始训练，设备: {self.device}")
        print(f"模型参数数量: {sum(p.numel() for p in self.model.parameters())}")
        
        start_time = time.time()
        
        for epoch in range(epochs):
            print(f'\nEpoch {epoch+1}/{epochs}')
            print('-' * 50)
            
            # 训练
            train_loss, train_acc = self.train_epoch(train_loader, criterion, optimizer)
            
            # 验证
            val_loss, val_acc = self.validate(val_loader, criterion)
            
            # 更新学习率
            scheduler.step(val_acc)
            
            # 记录历史
            self.train_history['loss'].append(train_loss)
            self.train_history['accuracy'].append(train_acc)
            self.train_history['val_loss'].append(val_loss)
            self.train_history['val_accuracy'].append(val_acc)
            
            print(f'Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.2f}%')
            print(f'Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.2f}%')
            
            # 保存最佳模型
            if val_acc > self.best_val_acc:
                self.best_val_acc = val_acc
                self.patience_counter = 0
                self.save_model(f'best_model_acc_{val_acc:.2f}.pth')
                print(f'新的最佳模型已保存! 验证准确率: {val_acc:.2f}%')
            else:
                self.patience_counter += 1
            
            # 早停
            if self.patience_counter >= patience:
                print(f'验证准确率连续{patience}个epoch未提升，早停训练')
                break
        
        total_time = time.time() - start_time
        print(f'\n训练完成! 总用时: {total_time:.2f}秒')
        print(f'最佳验证准确率: {self.best_val_acc:.2f}%')
        
        return self.train_history
    
    def save_model(self, filename):
        """保存模型"""
        filepath = os.path.join(self.save_path, filename)
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'best_val_acc': self.best_val_acc,
            'train_history': self.train_history
        }, filepath)
        
    def load_model(self, filepath):
        """加载模型"""
        checkpoint = torch.load(filepath, map_location=self.device)
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.best_val_acc = checkpoint.get('best_val_acc', 0.0)
        self.train_history = checkpoint.get('train_history', {})
        print(f"模型已从 {filepath} 加载")
