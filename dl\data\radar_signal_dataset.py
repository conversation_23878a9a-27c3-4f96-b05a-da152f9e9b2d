"""
雷达信号数据集
专门处理JSON格式的雷达信号数据，用于LSTM网络训练
"""
import os
import json
import glob
import torch
import numpy as np
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler


class RadarSignalDataset(Dataset):
    """雷达信号数据集类"""
    
    def __init__(self, data_dir, signal_type='heartbeat_signal_norm', sequence_length=512, 
                 transform=None, normalize=True):
        """
        初始化雷达信号数据集
        
        Args:
            data_dir: 数据目录路径，包含按标签分类的子文件夹
            signal_type: 要使用的信号类型 ('heartbeat_signal_norm', 'breathing_signal_norm', 'weighted_signal_norm')
            sequence_length: 序列长度，如果信号长度不足会进行填充，过长会截断
            transform: 数据变换函数
            normalize: 是否对信号进行标准化
        """
        self.data_dir = data_dir
        self.signal_type = signal_type
        self.sequence_length = sequence_length
        self.transform = transform
        self.normalize = normalize
        
        # 加载数据
        self.data, self.labels = self._load_data()
        
        # 标准化处理
        if self.normalize and len(self.data) > 0:
            self.scaler = StandardScaler()
            # 将所有数据展平进行标准化
            all_data = np.concatenate([signal.flatten() for signal in self.data])
            self.scaler.fit(all_data.reshape(-1, 1))
        else:
            self.scaler = None
    
    def _load_data(self):
        """加载数据和标签"""
        data = []
        labels = []

        # 获取所有子文件夹（标签文件夹）
        label_folders = [d for d in os.listdir(self.data_dir)
                        if os.path.isdir(os.path.join(self.data_dir, d)) and d.isdigit()]

        print(f"=" * 60)
        print(f"开始加载雷达信号数据集")
        print(f"=" * 60)
        print(f"数据目录: {self.data_dir}")
        print(f"信号类型: {self.signal_type}")
        print(f"序列长度: {self.sequence_length}")
        print(f"找到标签文件夹: {sorted(label_folders)}")
        print()

        for label_folder in sorted(label_folders):
            label = int(label_folder)
            folder_path = os.path.join(self.data_dir, label_folder)

            # 查找所有JSON文件
            json_files = glob.glob(os.path.join(folder_path, "*.json"))
            print(f"📁 标签 {label} (文件夹: {label_folder})")
            print(f"   找到 {len(json_files)} 个JSON文件")

            successful_files = 0
            failed_files = 0

            for i, json_file in enumerate(json_files):
                try:
                    signal = self._load_signal_from_json(json_file)
                    if signal is not None:
                        data.append(signal)
                        labels.append(label)
                        successful_files += 1

                        # 显示前几个文件的详细信息
                        if i < 3:  # 只显示前3个文件的详细信息
                            print(f"   ✅ 文件 {i+1}: {os.path.basename(json_file)}")
                            print(f"      信号形状: {signal.shape}")
                            print(f"      数据类型: {signal.dtype}")
                            print(f"      数值范围: [{signal.min():.4f}, {signal.max():.4f}]")
                            print(f"      均值: {signal.mean():.4f}, 标准差: {signal.std():.4f}")
                    else:
                        failed_files += 1
                        if i < 3:  # 只显示前几个失败的文件
                            print(f"   ❌ 文件 {i+1}: {os.path.basename(json_file)} - 未找到信号数据")
                except Exception as e:
                    failed_files += 1
                    if i < 3:  # 只显示前几个失败的文件
                        print(f"   ❌ 文件 {i+1}: {os.path.basename(json_file)} - 错误: {e}")
                    continue

            print(f"   📊 标签 {label} 统计: 成功 {successful_files} 个, 失败 {failed_files} 个")
            print()

        print(f"=" * 60)
        print(f"数据加载完成")
        print(f"=" * 60)
        print(f"总共加载了 {len(data)} 个信号样本")
        if len(data) > 0:
            print(f"样本形状: {data[0].shape}")
            print(f"数据类型: {data[0].dtype}")

            # 计算整体统计信息
            all_signals = np.array(data)
            print(f"整体数据统计:")
            print(f"  - 数据集形状: {all_signals.shape}")
            print(f"  - 数值范围: [{all_signals.min():.4f}, {all_signals.max():.4f}]")
            print(f"  - 均值: {all_signals.mean():.4f}")
            print(f"  - 标准差: {all_signals.std():.4f}")
        print()

        return data, labels
    
    def _load_signal_from_json(self, json_file):
        """从JSON文件中加载指定类型的信号"""
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # 根据JSON结构提取信号
        if 'clusters' in data and '0' in data['clusters']:
            # 从clusters中提取信号
            cluster_data = data['clusters']['0']
            if self.signal_type in cluster_data:
                signal = np.array(cluster_data[self.signal_type], dtype=np.float32)
                original_length = len(signal)
            else:
                # 显示可用的信号类型
                available_signals = list(cluster_data.keys())
                print(f"      ⚠️  在clusters['0']中未找到 '{self.signal_type}'")
                print(f"          可用信号类型: {available_signals}")
                return None
        elif self.signal_type in data:
            # 直接从根级别提取信号
            signal = np.array(data[self.signal_type], dtype=np.float32)
            original_length = len(signal)
        else:
            # 显示JSON文件的结构信息
            if 'clusters' in data:
                print(f"      ⚠️  JSON结构: 包含clusters，但clusters['0']中无 '{self.signal_type}'")
                if '0' in data['clusters']:
                    available_signals = list(data['clusters']['0'].keys())
                    print(f"          clusters['0']中可用信号: {available_signals}")
            else:
                available_keys = list(data.keys())
                print(f"      ⚠️  JSON结构: 根级别无 '{self.signal_type}'")
                print(f"          根级别可用键: {available_keys}")
            return None

        # 处理序列长度
        signal = self._process_sequence_length(signal)

        return signal
    
    def _process_sequence_length(self, signal):
        """处理序列长度：截断或填充"""
        if len(signal) > self.sequence_length:
            # 截断：取中间部分
            start_idx = (len(signal) - self.sequence_length) // 2
            signal = signal[start_idx:start_idx + self.sequence_length]
        elif len(signal) < self.sequence_length:
            # 填充：用零填充
            padding = self.sequence_length - len(signal)
            signal = np.pad(signal, (0, padding), mode='constant', constant_values=0)
        
        return signal
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        signal = self.data[idx].copy()
        label = self.labels[idx]
        
        # 标准化
        if self.scaler is not None:
            signal = self.scaler.transform(signal.reshape(-1, 1)).flatten()
        
        # 转换为张量
        signal = torch.FloatTensor(signal)
        label = torch.LongTensor([label])[0]
        
        # 为LSTM添加特征维度 (sequence_length, 1)
        signal = signal.unsqueeze(-1)
        
        # 应用变换
        if self.transform:
            signal = self.transform(signal)
        
        return signal, label
    
    def get_class_distribution(self):
        """获取类别分布"""
        unique_labels, counts = np.unique(self.labels, return_counts=True)
        return dict(zip(unique_labels, counts))


def get_radar_signal_data_loaders(data_dir, signal_type='heartbeat_signal_norm', 
                                 sequence_length=512, batch_size=32, val_split=0.2, 
                                 test_split=0.1, num_workers=4, normalize=True, 
                                 random_state=42):
    """
    获取雷达信号数据加载器
    
    Args:
        data_dir: 数据目录
        signal_type: 信号类型
        sequence_length: 序列长度
        batch_size: 批次大小
        val_split: 验证集比例
        test_split: 测试集比例
        num_workers: 工作进程数
        normalize: 是否标准化
        random_state: 随机种子
    
    Returns:
        train_loader, val_loader, test_loader
    """
    # 创建完整数据集
    full_dataset = RadarSignalDataset(
        data_dir=data_dir,
        signal_type=signal_type,
        sequence_length=sequence_length,
        normalize=normalize
    )
    
    if len(full_dataset) == 0:
        raise ValueError("数据集为空，请检查数据目录和文件格式")
    
    print(f"数据集信息:")
    print(f"- 总样本数: {len(full_dataset)}")
    print(f"- 信号类型: {signal_type}")
    print(f"- 序列长度: {sequence_length}")
    print(f"- 类别分布: {full_dataset.get_class_distribution()}")
    
    # 获取数据和标签用于分割
    indices = list(range(len(full_dataset)))
    labels = full_dataset.labels
    
    # 分割数据集
    train_indices, temp_indices = train_test_split(
        indices, test_size=(val_split + test_split), 
        stratify=labels, random_state=random_state
    )
    
    if test_split > 0:
        val_indices, test_indices = train_test_split(
            temp_indices, test_size=test_split/(val_split + test_split),
            stratify=[labels[i] for i in temp_indices], random_state=random_state
        )
    else:
        val_indices = temp_indices
        test_indices = []
    
    # 创建子数据集
    train_dataset = torch.utils.data.Subset(full_dataset, train_indices)
    val_dataset = torch.utils.data.Subset(full_dataset, val_indices)
    test_dataset = torch.utils.data.Subset(full_dataset, test_indices) if test_indices else None
    
    print(f"数据分割:")
    print(f"- 训练集: {len(train_dataset)} 样本")
    print(f"- 验证集: {len(val_dataset)} 样本")
    if test_dataset:
        print(f"- 测试集: {len(test_dataset)} 样本")
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset, batch_size=batch_size, shuffle=True, 
        num_workers=num_workers, pin_memory=torch.cuda.is_available()
    )
    
    val_loader = DataLoader(
        val_dataset, batch_size=batch_size, shuffle=False,
        num_workers=num_workers, pin_memory=torch.cuda.is_available()
    )
    
    test_loader = None
    if test_dataset:
        test_loader = DataLoader(
            test_dataset, batch_size=batch_size, shuffle=False,
            num_workers=num_workers, pin_memory=torch.cuda.is_available()
        )
    
    return train_loader, val_loader, test_loader


# 为了兼容性，创建一个包装函数
def get_data_loaders(data_dir, batch_size=32, val_split=0.2, num_workers=4,
                    image_size=None, use_augmentation=False, **kwargs):
    """
    兼容性函数，用于替换原有的get_data_loaders
    """
    # 获取绝对路径
    abs_data_dir = os.path.abspath(data_dir)
    print(f"检查数据目录: {data_dir} -> {abs_data_dir}")

    # 检查数据目录是否存在
    if not os.path.exists(abs_data_dir):
        # 尝试当前工作目录
        current_dir = os.getcwd()
        print(f"当前工作目录: {current_dir}")

        # 列出当前目录内容
        if os.path.exists(current_dir):
            current_items = os.listdir(current_dir)
            print(f"当前目录内容: {current_items}")

        raise ValueError(f"数据目录不存在: {data_dir} (绝对路径: {abs_data_dir})")

    # 获取目录中的所有项目
    try:
        items = os.listdir(abs_data_dir)
        print(f"数据目录 {abs_data_dir} 中的内容: {items}")

        # 检查是否有数字命名的文件夹
        digit_folders = [d for d in items if os.path.isdir(os.path.join(abs_data_dir, d)) and d.isdigit()]
        print(f"找到的数字文件夹: {digit_folders}")

        if digit_folders:
            # 使用雷达信号数据加载器
            return get_radar_signal_data_loaders(
                data_dir=abs_data_dir,
                batch_size=batch_size,
                val_split=val_split,
                num_workers=num_workers,
                **kwargs
            )
        else:
            raise ValueError(f"数据目录 {abs_data_dir} 中没有找到数字命名的标签文件夹")

    except Exception as e:
        raise ValueError(f"检查数据目录 {abs_data_dir} 时出错: {e}")


def main():
    """主函数：演示数据集加载和信息显示"""
    print("🚀 雷达信号数据集测试")
    print("=" * 80)

    # 配置参数 - 根据运行位置自动调整路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir.endswith(os.path.join('dl', 'data')):
        # 如果在dl/data目录下运行，使用当前目录
        data_dir = "."
    else:
        # 如果在项目根目录运行，使用dl/data
        data_dir = "dl/data"

    signal_type = "heartbeat_signal_norm"  # 信号类型
    sequence_length = 512  # 序列长度

    try:
        # 创建数据集
        print("正在创建雷达信号数据集...")
        dataset = RadarSignalDataset(
            data_dir=data_dir,
            signal_type=signal_type,
            sequence_length=sequence_length,
            normalize=True
        )

        if len(dataset) == 0:
            print("❌ 数据集为空！请检查数据目录和文件格式。")
            return

        # 显示类别分布
        class_dist = dataset.get_class_distribution()
        print("📊 类别分布:")
        for label, count in class_dist.items():
            print(f"   标签 {label}: {count} 个样本")
        print()

        # 测试几个样本
        print("🔍 样本测试:")
        for i in range(min(3, len(dataset))):
            signal, label = dataset[i]
            print(f"   样本 {i+1}:")
            print(f"     信号形状: {signal.shape}")
            print(f"     标签: {label}")
            print(f"     数据类型: {signal.dtype}")
            print(f"     数值范围: [{signal.min():.4f}, {signal.max():.4f}]")
        print()

        # 测试数据加载器
        print("🔄 测试数据加载器...")
        try:
            train_loader, val_loader, test_loader = get_radar_signal_data_loaders(
                data_dir=data_dir,
                signal_type=signal_type,
                sequence_length=sequence_length,
                batch_size=8,
                val_split=0.2,
                test_split=0.1,
                num_workers=0  # Windows上设为0
            )

            print("✅ 数据加载器创建成功！")
            print(f"   训练集批次数: {len(train_loader)}")
            print(f"   验证集批次数: {len(val_loader)}")
            if test_loader:
                print(f"   测试集批次数: {len(test_loader)}")

            # 测试一个批次
            print("\n🎯 测试批次数据:")
            for batch_signals, batch_labels in train_loader:
                print(f"   批次信号形状: {batch_signals.shape}")
                print(f"   批次标签形状: {batch_labels.shape}")
                print(f"   批次标签值: {batch_labels.tolist()}")
                break

        except Exception as e:
            print(f"❌ 数据加载器测试失败: {e}")

        print("\n" + "=" * 80)
        print("✅ 数据集测试完成！现在可以运行 dl/train.py 开始训练。")
        print("=" * 80)

    except Exception as e:
        print(f"❌ 数据集创建失败: {e}")
        print("请检查数据目录路径和文件格式。")


if __name__ == "__main__":
    main()
