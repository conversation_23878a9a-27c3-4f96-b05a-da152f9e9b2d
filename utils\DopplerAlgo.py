import numpy as np
from scipy import signal

from utils.My_utils import fft_spectrum


class DopplerAlgo:
    """Compute Range-Doppler map"""

    def __init__(self, num_samples: int, num_chirps_per_frame: int, num_ant: int, algo_type: str = 'doppler', mti_alpha:float = 0.8, mti=True):
        """Create Range-Doppler map object

        Parameters:
            - num_samples:          Number of samples in a single chirp
            - num_chirps_per_frame: Number of chirp repetitions within a measurement frame
            - num_ant:              Number of antennas
            - mti_alpha:            Parameter alpha of Moving Target Indicator
        """
        self.num_chirps_per_frame = num_chirps_per_frame

        # compute Blackman-Harris Window matrix over chirp samples(range)
        self.range_window = signal.blackmanharris(num_samples).reshape(1, num_samples)

        # compute Blackman-Harris Window matrix over number of chirps(velocity)
        self.doppler_window = signal.blackmanharris(self.num_chirps_per_frame).reshape(1, self.num_chirps_per_frame)

        # parameter for moving target indicator (MTI)
        self.mti_alpha = mti_alpha
        self.mti = mti

        self.algo_type = algo_type.lower()

        # initialize MTI filter
        self.mti_history = np.zeros((self.num_chirps_per_frame, num_samples, num_ant))

    def compute_doppler_map(self, data: np.ndarray, i_ant: int):
        """Compute Range-Doppler map for i-th antennas

        Parameter:
            - data:     Raw-data for one antenna (dimension:
                        num_chirps_per_frame x num_samples)
            - i_ant:    RX antenna index
        """
        # Step 1 - Remove average from signal (mean removal)
        data = data - np.average(data)

        if self.mti:
            # Step 2 - MTI processing to remove static objects
            data_mti = data - self.mti_history[:, :, i_ant]
            self.mti_history[:, :, i_ant] = data * self.mti_alpha + self.mti_history[:, :, i_ant] * (1 - self.mti_alpha)

            # Step 3 - calculate fft spectrum for the frame
            fft1d = fft_spectrum(data_mti, self.range_window)
        else:
            fft1d = fft_spectrum(data, self.range_window)
        fft1d[:, 0:8] = 0

        # Decide which algorithm to run based on the selected type
        if self.algo_type == 'range':
            # Step 4 - coppy
            reversed_fft = fft1d[::-1]
            # 合并原数组和反转后的数组
            fftd = np.concatenate((reversed_fft, fft1d))

            return np.transpose(fftd)

        elif self.algo_type == 'doppler':
            # prepare for doppler FFT
            # Transpose
            # Distance is now indicated on y axis
            fft1d = np.transpose(fft1d)

            # Step 4 - Windowing the Data in doppler
            fft1d = np.multiply(fft1d, self.doppler_window)

            zp2 = np.pad(fft1d, ((0, 0), (0, self.num_chirps_per_frame)), "constant")
            fft2d = np.fft.fft(zp2) / self.num_chirps_per_frame

            # re-arrange fft result for zero speed at centre
            return np.fft.fftshift(fft2d, (1,))
