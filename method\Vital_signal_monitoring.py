from scipy import constants
from scipy.ndimage import gaussian_filter1d

from utils.data_parse import RawParse
from utils.MUSIC import *
from utils.My_utils import normalized_signal, plot_signals_and_ffts
from position import position
from phase_diff_signal import extract_phase_diff_signal

from method.vital_signals import vital_signal_ours
# from utils.HRV import calculate_hrv

# np.set_printoptions(threshold=np.inf)

# 数据地址
# file_path = r"D:\PycharmProject\RADAR\HuayiHealthRadar_VSM\data\output\raw\20250104212150.dat"
file_path = r"D:\PycharmProject\RadarDL\data\.Angele0\radar\20250407100818.dat"

save_path = r"plot"
# 解析数据
raw_data = RawParse(file_path)
print(f"Frame Rate: {raw_data.header.frame_rate}  ;Chirp Num Per Frame: {raw_data.header.num_chirps_per_frame}  ;Sample Num Per Chirp: {raw_data.header.num_samples_per_chirp}")
print(f"Range Resolution: {((constants.c / (2 * (raw_data.header.end_frequency_Hz - raw_data.header.start_frequency_Hz)))*1000):.2f} mm  ;Max Range: {(((constants.c / (2 * (raw_data.header.end_frequency_Hz - raw_data.header.start_frequency_Hz)))/2)*raw_data.header.num_samples_per_chirp):.2f} m")
# 计算时间轴
total_frames = raw_data.total_frames - 5  # 减去边界帧
time_axis = np.arange(total_frames) * (1 / raw_data.header.frame_rate)
sample_rate = raw_data.header.frame_rate

# 展示空间定位图
target_clusters = position(raw_data=raw_data, mti_alpha=0.1,
                           num_beams=27, max_angle=45,
                           eps=5, min_samples=5, energy_threshold=0.995, position_threshold=0.1, plot=True)
raw_data.file.seek(0)
horizontal_phase_signals_dict, vertical_phase_signals_dict = extract_phase_diff_signal(
    raw_data=raw_data, target_clusters=target_clusters,
    num_beams=27, max_angle=45,
    x_offset=1, y_offset=1, z_offset=1)

weighted_signal, final_signals = vital_signal_ours(horizontal_phase_signals_dict, vertical_phase_signals_dict, sample_rate=sample_rate, bsnr=40, hsnr=25)

# 遍历每个簇的呼吸信号和心跳信号并展示
for label in final_signals.keys():
    print(f"------------------------------------------ Cluster {label} ------------------------------------------")
    # 获取当前簇的呼吸信号和心跳信号
    breathing_signal = final_signals[label][0]
    heartbeat_signal = final_signals[label][1]
    breathing_signal_norm = normalized_signal(breathing_signal)
    heartbeat_signal_norm = normalized_signal(heartbeat_signal)

    signals = [weighted_signal, breathing_signal_norm, heartbeat_signal_norm]
    titles = ["weighted_signal", "breathing_signal", "heartbeat_signal"]
    plot_signals_and_ffts(signals, titles, sample_rate, time_axis)

    _, _, estimated_freqs = MUSIC(heartbeat_signal_norm, fs=sample_rate, num_sources=2, plot=True)
    # calculate_hrv(estimated_freqs*60, heartbeat_signal_norm, sample_rate=sample_rate, time_axis=time_axis)
