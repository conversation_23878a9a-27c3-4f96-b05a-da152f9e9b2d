import numpy as np
import struct


class RawHeader(object):
    def __init__(self):
        self.sample_rate = 0
        self.rx_mask = 0
        self.tx_mask = 0
        self.tx_power_level = 0
        self.if_gain_db = 0
        self.start_frequency_Hz = 0
        self.end_frequency_Hz = 0
        self.num_samples_per_chirp = 0
        self.num_chirps_per_frame = 0
        self.chirp_repetition_time_s = 0
        self.frame_repetition_time_s = 0
        self.hp_cutoff_Hz = 0
        self.aaf_cutoff_Hz = 0
        self.mimo_mode = 0
        self.antenna_num = 0
        self.frame_rate = 0


class RawFrame(object):
    def __init__(self):
        self.index = 0
        self.ts = 0
        self.data = []


class RawParse(object):
    def __init__(self, file_name):
        self.file = open(file_name, 'rb')
        if self.file.readable():
            print('file is readable')
        self.header = RawHeader()
        self._parse_header_info()
        self.total_frames = 0
        self._calculate_total_frames()
        self._calculate_info()

    def _parse_header_info(self):
        self.eof = self.file.tell()
        self.file.seek(0, 0)
        bts = self.file.read(4)
        self.header.sample_rate = struct.unpack("I", bts)[0]
        bts = self.file.read(4)
        self.header.rx_mask = struct.unpack("I", bts)[0]
        bts = self.file.read(4)
        self.header.tx_mask = struct.unpack("I", bts)[0]
        bts = self.file.read(4)
        self.header.tx_power_level = struct.unpack("I", bts)[0]
        bts = self.file.read(4)
        self.header.if_gain_db = struct.unpack("I", bts)[0]
        bts = self.file.read(8)
        self.header.start_frequency_Hz = struct.unpack("Q", bts)[0]
        bts = self.file.read(8)
        self.header.end_frequency_Hz = struct.unpack("Q", bts)[0]
        bts = self.file.read(4)
        self.header.num_samples_per_chirp = struct.unpack("I", bts)[0]
        bts = self.file.read(4)
        self.header.num_chirps_per_frame = struct.unpack("I", bts)[0]
        bts = self.file.read(4)
        self.header.chirp_repetition_time_s = struct.unpack("f", bts)[0]
        bts = self.file.read(4)
        self.header.frame_repetition_time_s = struct.unpack("f", bts)[0]
        bts = self.file.read(4)
        self.header.hp_cutoff_Hz = struct.unpack("I", bts)[0]
        bts = self.file.read(4)
        self.header.aaf_cutoff_Hz = struct.unpack("I", bts)[0]
        bts = self.file.read(4)
        self.header.mimo_mode = struct.unpack("I", bts)[0]
        if self.header.rx_mask == 7:
            self.header.antenna_num = 3
        self.header.frame_rate = int(1 / self.header.frame_repetition_time_s)

    def _calculate_total_frames(self):
        # 保存当前文件指针位置
        current_pos = self.file.tell()
        # 定位到文件末尾
        self.file.seek(0, 2)
        file_size = self.file.tell()
        # 定位到文件头部
        self.file.seek(current_pos, 0)

        # 计算每帧的数据大小
        frame_data_size = 8 + 8 + (
                self.header.antenna_num * self.header.num_chirps_per_frame * self.header.num_samples_per_chirp * 4)
        # 计算总帧数
        self.total_frames = (file_size - current_pos) // frame_data_size

    def _calculate_info(self):
        self.sample_rate = self.header.frame_rate * self.header.num_chirps_per_frame * self.header.num_samples_per_chirp
        self.chirp_repetition_time = 1 / (self.header.frame_rate * self.header.num_chirps_per_frame)
        num_chirp = self.total_frames * self.header.num_chirps_per_frame
        self.total_time = num_chirp * self.chirp_repetition_time

    def get_next_frame(self):
        bts_1 = self.file.read(8)
        bts_2 = self.file.read(8)
        bts_3 = self.file.read(
            self.header.antenna_num * self.header.num_chirps_per_frame * self.header.num_samples_per_chirp * 4)
        frame = RawFrame()
        frame.index = struct.unpack("Q", bts_1)[0]
        frame.ts = struct.unpack("Q", bts_2)[0]
        frame.data = np.frombuffer(bts_3, dtype=np.float32).copy()
        frame.data.resize((self.header.antenna_num, self.header.num_chirps_per_frame, self.header.num_samples_per_chirp))
        return frame


def test():
    file_name = r"D:\PycharmProject\RADAR\HuayiHealthRadar_VSM\Range100\radar\20250312160550.dat"
    raw_data = RawParse(file_name)
    for frame_idx in range(0, raw_data.total_frames):
        frame = raw_data.get_next_frame()
        print("frame shape:", frame.data)

    print("Frame Rate:", raw_data.header.frame_rate)
    print("Chirp Num Per Frame:", raw_data.header.num_chirps_per_frame)
    print("Sample Num Per Chirp:", raw_data.header.num_samples_per_chirp)

    print("Sample Rate:", raw_data.sample_rate)
    print("Chirp Repetition Time:", raw_data.chirp_repetition_time)
    print("Total Frame:", raw_data.total_frames)
    print("Total Time:", raw_data.total_time)


if __name__ == '__main__':
    test()
