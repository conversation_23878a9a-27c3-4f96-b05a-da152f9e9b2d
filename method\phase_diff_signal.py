import numpy as np

from utils.DigitalBeamForming import DigitalBeamForming
from utils.DopplerAlgo import DopplerAlgo
# np.set_printoptions(threshold=np.inf)
# 提取指定天线的相位差信号

def process_clusters_with_offset(target_clusters,
                                 x_offset=2, y_offset=2, z_offset=2,
                                 max_x=26, max_y=127, max_z=26):
    """
    对target_clusters中的每个簇进行处理，扩展每个点的坐标，前后加减offset个单位。
    如果生成的坐标有重复则不保存。

    :param target_clusters: 包含每个簇的坐标，格式为{label: [(x_idx, y_idx, z_idx), ...]}
    :param offset: 扩展的坐标范围，默认为2。
    :return: 处理后的target_clusters，扩展坐标。
    """
    new_clusters = {}

    for label, coordinates in target_clusters.items():
        new_coords = set(coordinates)  # 使用集合去重

        for x_idx, y_idx, z_idx in coordinates:
            # 对y坐标进行扩展，前后加减y_offset个单位
            for delta_x in range(-x_offset, x_offset + 1):
                new_x_idx = x_idx + delta_x
                new_coord = (new_x_idx, y_idx, z_idx)

                # 如果生成的坐标没有出现过，则添加到集合中
                if new_coord not in new_coords and 0 <= new_x_idx <= max_x:
                    new_coords.add(new_coord)
            for delta_y in range(-y_offset, y_offset + 1):
                new_y_idx = y_idx + delta_y
                new_coord = (x_idx, new_y_idx, z_idx)

                # 如果生成的坐标没有出现过，则添加到集合中
                if new_coord not in new_coords and 0 <= new_y_idx <= max_y:
                    new_coords.add(new_coord)
            for delta_z in range(-z_offset, z_offset + 1):
                new_z_idx = z_idx + delta_z
                new_coord = (x_idx, y_idx, new_z_idx)

                # 如果生成的坐标没有出现过，则添加到集合中
                if new_coord not in new_coords and 0 <= new_z_idx <= max_z:
                    new_coords.add(new_coord)

        new_clusters[label] = list(new_coords)  # 将集合转为列表保存

    return new_clusters


# 删除方差低于阈值的目标点
def filter_low_variance_points(phase_signals_dict, variance_threshold):
    filtered_signals_dict = {}
    for label, signals in phase_signals_dict.items():
        # 计算每个目标点的方差
        variance = np.var(signals, axis=1)
        # 保留方差大于阈值的点
        valid_indices = np.where(variance >= variance_threshold)[0]
        # # 打印被删除的信号
        # invalid_indices = np.where(variance < variance_threshold)[0]
        # for idx in invalid_indices:
        #     print(f"已删除 Target {label} 的信号 {idx}，方差为 {variance[idx]:.4f}")
        if len(valid_indices) == 0:
            print(f"All signals for Target {label} were deleted. Keeping one row of zeros.")
            filtered_signals_dict[label] = np.zeros((1, signals.shape[1]))
        else:
            filtered_signals_dict[label] = signals[valid_indices, :]
    return filtered_signals_dict

def extract_phase_diff_signal(raw_data, target_clusters,
                              num_beams=27, max_angle=45,
                              x_offset=2, y_offset=2, z_offset=2):
    # 打印初始坐标点
    for label, coordinates in target_clusters.items():
        print(f"Predict Target {label}-------------------Number of points: {len(coordinates)}\nCoordinates: {coordinates}")
    target_clusters = process_clusters_with_offset(target_clusters=target_clusters, x_offset=x_offset, y_offset=y_offset, z_offset=z_offset,
                                                    max_x=num_beams-1, max_y=raw_data.header.num_samples_per_chirp-1, max_z=num_beams-1)
    # 打印扩展后坐标点
    for label, coordinates in target_clusters.items():
        print(f"Estimate Target {label}-------------------Number of points: {len(coordinates)}\nCoordinates: {coordinates}")
    # 获取天线数量和其他参数
    first_frame = raw_data.get_next_frame()
    num_ant = first_frame.data.shape[0]
    num_samples = first_frame.data.shape[2]
    num_chirps_per_frame = first_frame.data.shape[1]

    # 创建 RangeFFTAlgo 实例
    range_fft_algo = DopplerAlgo(
        num_samples=num_samples,
        num_chirps_per_frame=num_chirps_per_frame,
        num_ant=num_ant,
        algo_type="range",
        mti_alpha=1,
        mti=False)

    # 创建 DigitalBeamForming 实例
    dbf = DigitalBeamForming(num_antennas=2, num_beams=num_beams, max_angle_degrees=max_angle)  # 水平角度使用 0 和 2 号天线

    start_frame = 50
    end_frame = raw_data.total_frames - 5

    # 初始化字典，用于存储每个簇的水平和垂直相位信号
    horizontal_phase_signals_dict = {label: np.zeros((len(coords), end_frame - start_frame))
                                     for label, coords in target_clusters.items()}
    vertical_phase_signals_dict = {label: np.zeros((len(coords), end_frame - start_frame))
                                   for label, coords in target_clusters.items()}

    for frame_idx in range(start_frame, end_frame):
        frame_data = raw_data.get_next_frame().data  # [num_ant, num_chirps, num_samples] 实

        # 对每个天线的数据进行处理
        fft_data_list = []
        for ant_idx in range(num_ant):
            single_ant_data = frame_data[ant_idx, :, :]  # [num_chirps, num_samples] 实
            range_fft = range_fft_algo.compute_doppler_map(single_ant_data, i_ant=ant_idx)  # [num_samples, 2 * num_chirps] 复
            fft_data_list.append(range_fft)

        fft_data_combined = np.stack(fft_data_list, axis=-1)  # [num_samples, 2 * num_chirps, num_ant] 复

        # 处理水平角度的波束成形（使用天线 0 和 2）
        fft_data_horizontal = fft_data_combined[:, :, [0, 2]]  # 提取天线 0 和 2 的 FFT 数据
        result_horizontal = dbf.run(fft_data_horizontal)  # [num_samples, 2 * num_chirps, num_beams_horizontal] 复
        # 处理垂直角度的波束成形（使用天线 1 和 2）
        fft_data_vertical = fft_data_combined[:, :, [1, 2]]  # 提取天线 1 和 2 的 FFT 数据
        result_vertical = dbf.run(fft_data_vertical)  # [num_samples, 2 * num_chirps, num_beams_vertical] 复

        for label, coordinates in target_clusters.items():
            # 根据展平后的peak_idx中的[x, y, z]索引提取每个点的相位数据
            for i, (x_idx, y_idx, z_idx) in enumerate(coordinates):
                # 提取水平角度相位数据
                horizontal_phase = np.angle(result_horizontal[y_idx, :, x_idx])  # 获取相位
                # 检查水平相位数据是否为空或全零，并设置默认值
                if horizontal_phase.size == 0 or np.all(horizontal_phase == 0):
                    horizontal_phase_mean = 0.0
                else:
                    horizontal_phase_mean = horizontal_phase.mean()
                # 取平均，得到水平相位
                horizontal_phase_signals_dict[label][i, frame_idx - start_frame] = horizontal_phase_mean
                # 提取垂直角度相位数据
                vertical_phase = np.angle(result_vertical[y_idx, :, z_idx])  # 获取相位
                # 检查垂直相位数据是否为空或全零，并设置默认值
                if vertical_phase.size == 0 or np.all(vertical_phase == 0):
                    vertical_phase_mean = 0.0
                else:
                    vertical_phase_mean = vertical_phase.mean()
                # 取平均，得到垂直相位
                vertical_phase_signals_dict[label][i, frame_idx - start_frame] = vertical_phase_mean

    horizontal_phase_signals_dict = filter_low_variance_points(horizontal_phase_signals_dict, variance_threshold=3.0)
    vertical_phase_signals_dict = filter_low_variance_points(vertical_phase_signals_dict, variance_threshold=3.0)

    # 原始相位信号
    horizontal_diff_phase_signals_dict = {label: np.zeros((len(coords), end_frame - start_frame))
                                     for label, coords in target_clusters.items()}
    vertical_diff_phase_signals_dict = {label: np.zeros((len(coords), end_frame - start_frame))
                                   for label, coords in target_clusters.items()}

    for label in horizontal_phase_signals_dict.keys():
        horizontal_unwrap_phase_signals = np.unwrap(horizontal_phase_signals_dict[label], axis=1)  # 解缠
        horizontal_diff_phase_signals_dict[label] = horizontal_unwrap_phase_signals

        vertical_unwrap_phase_signals = np.unwrap(vertical_phase_signals_dict[label], axis=1)
        vertical_diff_phase_signals_dict[label] = vertical_unwrap_phase_signals

    return horizontal_diff_phase_signals_dict, vertical_diff_phase_signals_dict

from utils.data_parse import RawParse
from method.position import position
from utils.My_utils import *
from scipy import constants
from scipy.ndimage import gaussian_filter1d

def test():
    file_path = r"D:\PycharmProject\RADAR\HuayiHealthRadar_VSM\data\output\raw\20241107093602.dat"
    # 解析数据
    raw_data = RawParse(file_path)
    print(
        f"Frame Rate: {raw_data.header.frame_rate}  ;Chirp Num Per Frame: {raw_data.header.num_chirps_per_frame}  ;Sample Num Per Chirp: {raw_data.header.num_samples_per_chirp}")
    print(
        f"Range Resolution: {((constants.c / (2 * (raw_data.header.end_frequency_Hz - raw_data.header.start_frequency_Hz))) * 1000):.2f} mm  ;Max Range: {(((constants.c / (2 * (raw_data.header.end_frequency_Hz - raw_data.header.start_frequency_Hz))) / 2) * raw_data.header.num_samples_per_chirp):.2f} m")
    total_frames = raw_data.total_frames - 5  # 减去边界帧
    time_axis = np.arange(total_frames) * (1 / raw_data.header.frame_rate)
    sample_rate = raw_data.header.frame_rate

    # 展示空间定位图
    target_clusters = position(raw_data=raw_data, mti_alpha=0.1,
                                num_beams=27, max_angle=45,
                                eps=5, min_samples=5, energy_threshold=0.995, position_threshold=0.1, plot=True)
    raw_data.file.seek(0)
    horizontal_phase_signals_dict, vertical_phase_signals_dict = extract_phase_diff_signal(
        raw_data=raw_data, target_clusters=target_clusters,
        num_beams=27,  max_angle=45,
        x_offset=2, y_offset=2, z_offset=2)

    # 为每个簇分别计算并绘制水平和垂直相位信号
    for label in horizontal_phase_signals_dict.keys():
        # 打印水平相位信号
        print(f"Processing signals for Target {label} ---------------------")
        horizontal_phase_signals = horizontal_phase_signals_dict[label]
        vertical_phase_signals = vertical_phase_signals_dict[label]

        for i in range(0, horizontal_phase_signals.shape[0], 1):
            # 打印当前两组信号的信息
            for j in range(i, min(i + 1, horizontal_phase_signals.shape[0])):
                print(f"===============================================================我是分割线{j}===============================================================")

                def envelope(signal, window_size):
                    # 初始化上包络线和下包络线
                    upper_envelope = np.zeros_like(signal)
                    lower_envelope = np.zeros_like(signal)

                    for i in range(0, len(signal), window_size):
                        # 获取当前窗口中的信号片段
                        window = signal[i:i + window_size]
                        if len(window) > 0:
                            # 获取窗口内的最大值和最小值
                            upper_envelope[i:i + window_size] = np.max(window)
                            lower_envelope[i:i + window_size] = np.min(window)

                    return upper_envelope, lower_envelope

                upper_envelope, lower_envelope = envelope(horizontal_phase_signals[j], window_size=20)
                mean_envelope = (upper_envelope + lower_envelope) / 2
                heartbeat_signal = horizontal_phase_signals[j] - mean_envelope

                _, heartbeat_fre, _ = plot_rate(bandpass_filter(heartbeat_signal, lowcut=0.8, highcut=2.2, fs=sample_rate,order=1), sample_rate=sample_rate)
                final_signal = gaussian_filter1d(bandpass_filter(heartbeat_signal, lowcut=heartbeat_fre-0.2, highcut=heartbeat_fre+0.2, fs=sample_rate, order=1), sigma=5)

                # Organize signals for visualization
                horizontal_signals = [horizontal_phase_signals[j], mean_envelope, heartbeat_signal, final_signal]
                horizontal_titles = ["Original Signal", "Mean Envelope", "1", "2"]
                # Visualize signals and their FFTs
                plot_signals_and_ffts(horizontal_signals, horizontal_titles, sample_rate, time_axis)


            # # 同样的处理方式应用于垂直相位信号
            # for j in range(i, min(i + 1, vertical_phase_signals.shape[0])):
            #     print(f"===============================================================我是分割线{j}===============================================================")
            #     breathing_signal = bandpass_filter(ICA(vertical_phase_signals[j], confidence_level=0.99, mode="soft"),
            #                                        lowcut=0.1, highcut=0.5, fs=sample_rate, order=2)
            #     print(f"Vertical signal {j}: 呼吸信号信噪比为：{SNR(breathing_signal, sample_rate)}")
            #     heartbeat_signal = bandpass_filter(ICA(vertical_phase_signals[j], confidence_level=0.99, mode="soft") - breathing_signal,
            #                                        lowcut=0.8,highcut=2.2, fs=sample_rate, order=2)
            #     print(f"Vertical signal {j}: 心跳信号信噪比为：{SNR(heartbeat_signal, sample_rate)}")
            #
            #     vertical_signals = [vertical_phase_signals[j], ICA(vertical_phase_signals[j], confidence_level=0.99, mode="soft"), breathing_signal, heartbeat_signal]
            #     vertical_titles = ["horizontal_phase signals", "ICA(vertical_phase_signals[j])", "breathing_signal", "heartbeat_signal"]
            #     plot_signals_and_ffts(vertical_signals, vertical_titles, sample_rate, time_axis)


if __name__ == "__main__":
    test()
