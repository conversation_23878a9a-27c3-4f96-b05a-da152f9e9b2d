"""
模型训练脚本
"""
import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
import random
import numpy as np
from tqdm import tqdm
import time
import json

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from dl.train_config import TrainConfig
from dl.models import SimpleLSTM
from dl.data import get_data_loaders


def set_seed(seed=42):
    """设置随机种子以确保结果可复现"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False


def train_epoch(model, train_loader, criterion, optimizer, device):
    """训练一个epoch"""
    model.train()
    total_loss = 0
    correct = 0
    total = 0

    progress_bar = tqdm(train_loader, desc="训练", leave=True)

    for _, (data, target) in enumerate(progress_bar):
        data, target = data.to(device), target.to(device)

        optimizer.zero_grad()
        output = model(data)
        loss = criterion(output, target)
        loss.backward()
        optimizer.step()

        total_loss += loss.item()
        pred = output.argmax(dim=1, keepdim=True)
        correct += pred.eq(target.view_as(pred)).sum().item()
        total += target.size(0)

        # 更新进度条
        progress_bar.set_postfix({
            'Loss': f'{loss.item():.4f}',
            'Acc': f'{100.*correct/total:.2f}%'
        })

    avg_loss = total_loss / len(train_loader)
    accuracy = 100. * correct / total

    return avg_loss, accuracy


def validate_epoch(model, val_loader, criterion, device):
    """验证一个epoch"""
    model.eval()
    total_loss = 0
    correct = 0
    total = 0

    with torch.no_grad():
        progress_bar = tqdm(val_loader, desc="验证", leave=True)

        for data, target in progress_bar:
            data, target = data.to(device), target.to(device)
            output = model(data)
            loss = criterion(output, target)

            total_loss += loss.item()
            pred = output.argmax(dim=1, keepdim=True)
            correct += pred.eq(target.view_as(pred)).sum().item()
            total += target.size(0)

            # 更新进度条
            progress_bar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Acc': f'{100.*correct/total:.2f}%'
            })

    avg_loss = total_loss / len(val_loader)
    accuracy = 100. * correct / total

    return avg_loss, accuracy


def save_checkpoint(model, optimizer, epoch, best_acc, save_path):
    """保存检查点"""
    checkpoint = {
        'epoch': epoch,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'best_acc': best_acc,
    }
    torch.save(checkpoint, save_path)


def save_training_history(history, save_path):
    """保存训练历史"""
    with open(save_path, 'w', encoding='utf-8') as f:
        json.dump(history, f, indent=2)


def load_checkpoint(checkpoint_path, model, optimizer=None):
    """加载检查点"""
    if not os.path.exists(checkpoint_path):
        raise FileNotFoundError(f"检查点文件不存在: {checkpoint_path}")

    print(f"📂 加载检查点: {checkpoint_path}")
    checkpoint = torch.load(checkpoint_path, map_location='cpu')

    # 加载模型权重
    model.load_state_dict(checkpoint['model_state_dict'])

    # 加载优化器状态（如果提供）
    if optimizer is not None and 'optimizer_state_dict' in checkpoint:
        optimizer.load_state_dict(checkpoint['optimizer_state_dict'])

    # 获取训练信息
    start_epoch = checkpoint.get('epoch', 0) + 1
    best_acc = checkpoint.get('best_acc', 0.0)

    print(f"✅ 检查点加载成功")
    print(f"   上次训练到第 {checkpoint.get('epoch', 0)} 轮")
    print(f"   最佳验证准确率: {best_acc:.2f}%")
    print(f"   将从第 {start_epoch} 轮继续训练")

    return start_epoch, best_acc


def main():
    """主训练函数"""
    print("🚀 开始训练雷达信号分类模型")

    # 验证并打印配置
    TrainConfig.validate_config()
    TrainConfig.print_config()

    # 设置随机种子
    set_seed(TrainConfig.RANDOM_SEED)

    # 创建必要的目录
    os.makedirs(TrainConfig.MODEL_SAVE_PATH, exist_ok=True)
    os.makedirs(TrainConfig.LOG_DIR, exist_ok=True)

    print(f"\n🖥️  使用设备: {TrainConfig.DEVICE}")
    if torch.cuda.is_available():
        print(f"GPU: {torch.cuda.get_device_name(0)}")
        print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")

    # 加载数据
    print("\n📊 加载数据...")
    train_loader, val_loader, test_loader = get_data_loaders(
        data_dir=TrainConfig.DATA_DIR,
        signal_type=TrainConfig.SIGNAL_TYPE,
        sequence_length=TrainConfig.SEQUENCE_LENGTH,
        batch_size=TrainConfig.BATCH_SIZE,
        val_split=TrainConfig.VAL_SPLIT,
        test_split=TrainConfig.TEST_SPLIT,
        num_workers=TrainConfig.NUM_WORKERS
    )
    
    print(f"📈 训练集大小: {len(train_loader.dataset)}")
    print(f"📈 验证集大小: {len(val_loader.dataset)}")
    if test_loader:
        print(f"📈 测试集大小: {len(test_loader.dataset)}")

    # 创建模型
    print("\n🧠 创建模型...")
    model = SimpleLSTM(
        input_size=TrainConfig.INPUT_SIZE,
        hidden_size=TrainConfig.HIDDEN_SIZE,
        num_layers=TrainConfig.NUM_LAYERS,
        num_classes=TrainConfig.NUM_CLASSES,
        dropout_rate=TrainConfig.DROPOUT_RATE
    ).to(TrainConfig.DEVICE)

    # 打印模型信息
    total_params = sum(p.numel() for p in model.parameters())
    print(f"📊 模型参数数量: {total_params:,}")

    # 创建损失函数和优化器
    criterion = nn.CrossEntropyLoss()

    if TrainConfig.OPTIMIZER == "Adam":
        optimizer = optim.Adam(model.parameters(), lr=TrainConfig.LEARNING_RATE,
                              weight_decay=TrainConfig.WEIGHT_DECAY, betas=TrainConfig.BETAS)
    elif TrainConfig.OPTIMIZER == "SGD":
        optimizer = optim.SGD(model.parameters(), lr=TrainConfig.LEARNING_RATE,
                             weight_decay=TrainConfig.WEIGHT_DECAY, momentum=TrainConfig.MOMENTUM)
    elif TrainConfig.OPTIMIZER == "AdamW":
        optimizer = optim.AdamW(model.parameters(), lr=TrainConfig.LEARNING_RATE,
                               weight_decay=TrainConfig.WEIGHT_DECAY, betas=TrainConfig.BETAS)

    # 学习率调度器
    if TrainConfig.USE_SCHEDULER:
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, mode='max', factor=TrainConfig.SCHEDULER_FACTOR,
            patience=TrainConfig.SCHEDULER_PATIENCE, verbose=True,
            min_lr=TrainConfig.SCHEDULER_MIN_LR
        )
    else:
        scheduler = None

    # 训练历史记录
    history = {
        'train_loss': [],
        'train_acc': [],
        'val_loss': [],
        'val_acc': [],
        'lr': []
    }

    # 检查是否从检查点继续训练
    start_epoch = TrainConfig.START_EPOCH
    best_val_acc = 0.0
    patience_counter = 0

    if TrainConfig.RESUME_TRAINING and TrainConfig.CHECKPOINT_PATH:
        try:
            if TrainConfig.LOAD_OPTIMIZER:
                start_epoch, best_val_acc = load_checkpoint(TrainConfig.CHECKPOINT_PATH, model, optimizer)
            else:
                start_epoch, best_val_acc = load_checkpoint(TrainConfig.CHECKPOINT_PATH, model)
        except Exception as e:
            print(f"❌ 加载检查点失败: {e}")
            print("将从头开始训练...")
            start_epoch = TrainConfig.START_EPOCH
            best_val_acc = 0.0

    print(f"\n🏃 开始训练... (从第 {start_epoch} 轮到第 {TrainConfig.EPOCHS} 轮)")
    print("=" * 80)

    for epoch in range(start_epoch, TrainConfig.EPOCHS + 1):
        start_time = time.time()

        # 训练
        train_loss, train_acc = train_epoch(model, train_loader, criterion, optimizer, TrainConfig.DEVICE)

        # 验证
        val_loss, val_acc = validate_epoch(model, val_loader, criterion, TrainConfig.DEVICE)

        # 学习率调度
        if scheduler:
            scheduler.step(val_acc)
        current_lr = optimizer.param_groups[0]['lr']

        # 记录历史
        history['train_loss'].append(train_loss)
        history['train_acc'].append(train_acc)
        history['val_loss'].append(val_loss)
        history['val_acc'].append(val_acc)
        history['lr'].append(current_lr)

        # 计算时间
        epoch_time = time.time() - start_time

        # 打印结果
        print(f"Epoch {epoch:3d}/{TrainConfig.EPOCHS} | "
              f"Train Loss: {train_loss:.4f} | Train Acc: {train_acc:6.2f}% | "
              f"Val Loss: {val_loss:.4f} | Val Acc: {val_acc:6.2f}% | "
              f"LR: {current_lr:.6f} | Time: {epoch_time:.1f}s")

        # 保存最佳模型
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            patience_counter = 0

            # 保存最佳模型
            best_model_path = os.path.join(TrainConfig.MODEL_SAVE_PATH, 'best_model.pth')
            save_checkpoint(model, optimizer, epoch, best_val_acc, best_model_path)
            print(f"💾 保存最佳模型 (验证准确率: {best_val_acc:.2f}%)")
        else:
            patience_counter += 1

        # 早停检查
        if patience_counter >= TrainConfig.PATIENCE:
            print(f"\n⏹️  早停触发 (连续 {TrainConfig.PATIENCE} 轮无改善)")
            break

        # 定期保存检查点
        if epoch % TrainConfig.SAVE_FREQ == 0:
            checkpoint_path = os.path.join(TrainConfig.MODEL_SAVE_PATH, f'checkpoint_epoch_{epoch}.pth')
            save_checkpoint(model, optimizer, epoch, best_val_acc, checkpoint_path)

        print("-" * 80)

    # 保存训练历史
    history_path = os.path.join(TrainConfig.MODEL_SAVE_PATH, 'training_history.json')
    save_training_history(history, history_path)

    print("\n🎉 训练完成!")
    print(f"最佳验证准确率: {best_val_acc:.2f}%")
    print(f"模型保存路径: {TrainConfig.MODEL_SAVE_PATH}")
    print(f"训练历史保存: {history_path}")


if __name__ == "__main__":
    main()
