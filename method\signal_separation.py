from utils.My_utils import *
import numpy as np
from scipy.sparse.linalg import eigsh
import time
from method.position import position

def signal_separation(y_t, n=5, k=2, alpha_b=None, alpha_h=None, beta=None, gamma=None, rho=0.001, fs=100, max_iter=100, tol=0.16):
    """
    信号分离算法实现

    输入参数：
    - y_t: 观测信号，一维numpy数组，长度为 T
    - n: 低秩近似的秩（前 n 个特征值和特征向量）
    - alpha_b, alpha_h, beta, gamma: 权重系数
    - rho: 步长参数（用于更新拉格朗日乘子）
    - fs: 采样频率（用于滤波器）
    - max_iter: 最大迭代次数
    - tol: 收敛阈值

    输出：
    - x_b: 分离出的信号 x_b(t)
    - x_h: 分离出的信号 x_h(t)
    """
    y_t = normalized_signal(y_t)
    T = len(y_t)  # 信号长度

    # 构建 Hankel 矩阵的尺寸
    m = T // 2
    K = T - m + 1  # Hankel 矩阵的列数

    # 初始化 x_b 和 x_h
    # 使用包络线方法初始化
    upper_envelope, lower_envelope = envelope(y_t, window_size=20)
    mean_envelope = (upper_envelope + lower_envelope) / 2

    # 使用带通滤波器进行初始分离
    x_b = bandpass_filter(mean_envelope, lowcut=0.1, highcut=0.5, fs=fs, order=2)
    x_h = bandpass_filter(y_t - mean_envelope, lowcut=0.8, highcut=2.2, fs=fs, order=5)

    # 初始化拉格朗日乘子 mu_b^i 和 mu_h^i
    mu_b = np.ones(n)  # 拉格朗日乘子 mu_b^i
    mu_h = np.ones(n)  # 拉格朗日乘子 mu_h^i
    divergence_count = 0

    # 预计算 y_t 的二阶导数
    def gradient_process(y_t, n):
        if n not in [1, 2]:
            raise ValueError(f"只能是1阶或者2阶，{n}阶引入强烈边界反应不可用")
        for _ in range(n):
            padded_y_t = np.pad(y_t, pad_width=5, mode='reflect')
            y_t = np.gradient(padded_y_t)[5:-5]
        return y_t
    y_t_dd = bandpass_filter(gradient_process(y_t, n=k), lowcut=0.8, highcut=2.2, fs=fs, order=5)
    # 获取 x_h 的最大值和最小值
    x_h_min = np.min(x_h)
    x_h_max = np.max(x_h)
    # 将 y_t_dd 归一化到 x_h 的最大值和最小值之间
    y_t_dd = x_h_min + (y_t_dd - np.min(y_t_dd)) * (x_h_max - x_h_min) / (np.max(y_t_dd) - np.min(y_t_dd))

    diff_b_prev = np.inf  # 上一轮迭代的呼吸信号差异
    diff_h_prev = np.inf  # 上一轮迭代的心跳信号差异
    for iter_num in range(max_iter):
        time_o = time.time()

        # 保存上一轮的 x_b 和 x_h，用于收敛判断
        x_b_prev = x_b.copy()
        x_h_prev = x_h.copy()

        # 对信号进行镜像填充
        padding_length = 10
        x_b_padded = np.pad(x_b, (padding_length, padding_length), mode='reflect')
        x_h_padded = np.pad(x_h, (padding_length, padding_length), mode='reflect')
        # 构建 x_b 的 Hankel 矩阵
        H_x_b = np.array([x_b_padded[i:i + K] for i in range(m)])
        # 构建 x_h 的 Hankel 矩阵
        H_x_h = np.array([x_h_padded[i:i + K] for i in range(m)])

        # 计算协方差矩阵
        R_b = (1 / K) * H_x_b @ H_x_b.T
        R_h = (1 / K) * H_x_h @ H_x_h.T

        # 对协方差矩阵进行特征值分解，计算前 n 个特征值和特征向量
        lambda_b, u_b = eigsh(R_b, k=n, which='LM')
        lambda_h, u_h = eigsh(R_h, k=n, which='LM')

        # 特征值和特征向量按降序排列
        idx_b = np.argsort(-lambda_b)
        lambda_b = lambda_b[idx_b]
        u_b = u_b[:, idx_b]

        idx_h = np.argsort(-lambda_h)
        lambda_h = lambda_h[idx_h]
        u_h = u_h[:, idx_h]

        # 更新 λ_b 和 λ_h
        # 利用拉格朗日函数对 λ 求导得到的显式更新公式
        lambda_b = -mu_b / (2 * alpha_b)
        lambda_h = -mu_h / (2 * alpha_h)

        # 确保 λ_b 和 λ_h 非负
        # lambda_b = np.maximum(lambda_b, 0)
        # lambda_h = np.maximum(lambda_h, 0)
        lambda_b = np.abs(lambda_b)
        lambda_h = np.abs(lambda_h)

        # 计算奇异值矩阵
        Sigma_b = np.diag(np.sqrt(K*lambda_b))
        Sigma_h = np.diag(np.sqrt(K*lambda_h))

        # 计算右奇异向量 V_b 和 V_h
        # 需要处理可能的除零或奇异矩阵问题
        try:
            V_b = (H_x_b.T @ u_b) @ np.linalg.pinv(Sigma_b)
            V_h = (H_x_h.T @ u_h) @ np.linalg.pinv(Sigma_h)
        except np.linalg.LinAlgError:
            print("矩阵求逆失败，可能存在奇异矩阵")
            break

        # 重建近似的 Hankel 矩阵
        H_approx_x_b = u_b @ Sigma_b @ V_b.T
        H_approx_x_h = u_h @ Sigma_h @ V_h.T
        # 从 Hankel 矩阵重建信号
        # 对反对角线求平均
        def reconstruct_signal_from_hankel(H):
            T_recon = H.shape[0] + H.shape[1] - 1
            x = np.zeros(T_recon)
            counts = np.zeros(T_recon)
            indices = np.add.outer(np.arange(H.shape[0]), np.arange(H.shape[1]))
            np.add.at(x, indices.flatten(), H.flatten())
            np.add.at(counts, indices.flatten(), 1)
            x = x / counts
            return x

        x_b_recon = reconstruct_signal_from_hankel(H_approx_x_b)
        x_h_recon = reconstruct_signal_from_hankel(H_approx_x_h)

        # 更新 x_b(t)
        numerator_b = y_t - x_h_prev
        for i in range(n):
            s_b_i = H_approx_x_b.T @ u_b[:, i]
            # Compute D_b_i^T s_b_i using convolution
            D_b_i_T_s_b_i = np.convolve(s_b_i, u_b[:, i][::-1], mode='full')

            # 计算重叠次数
            overlap_count_b = np.convolve(np.ones_like(s_b_i), np.ones_like(u_b[:, i]), mode='full')
            D_b_i_T_s_b_i /= overlap_count_b  # 归一化卷积结果
            numerator_b -= (mu_b[i] / (gamma * K)) * D_b_i_T_s_b_i
        x_b = numerator_b

        # 更新 x_h(t)
        numerator_h = gamma * (y_t - x_b) + beta * y_t_dd
        for i in range(n):
            s_h_i = H_approx_x_h.T @ u_h[:, i]
            # Compute D_h_i^T s_h_i using convolution
            D_h_i_T_s_h_i = np.convolve(s_h_i, u_h[:, i][::-1], mode='full')

            # 计算重叠次数
            overlap_count_h = np.convolve(np.ones_like(s_h_i), np.ones_like(u_h[:, i]), mode='full')
            D_h_i_T_s_h_i /= overlap_count_h  # 归一化卷积结果

            numerator_h -= (mu_h[i] / K) * D_h_i_T_s_h_i
        x_h = numerator_h / (gamma + beta)

        # 比较更新的信号和上一次信号，判断收敛
        diff_b = np.linalg.norm(x_b-x_b_prev)
        diff_h = np.linalg.norm(x_h-x_h_prev)
        print(f"算法在第 {iter_num+1} 次迭代时,呼吸信号差异为{diff_b}, 心跳信号差异为{diff_h}, 收敛阈值为:{tol}")
        if diff_b < tol and diff_h < tol:
            print(f"算法在第 {iter_num + 1} 次迭代时收敛")
            break

        # 判断差异变化是否小于差异变化阈值
        if (diff_b - diff_b_prev) > 0 and (diff_h - diff_h_prev) > 0:
            divergence_count += 1
            if divergence_count >= 3:   #====================阈值为3
                print(f"收敛于第 {iter_num + 1} 次迭代（算法发散）")
                break
        else:
            divergence_count += 0

        # 保存当前差异用于下一轮的比较
        diff_b_prev = diff_b
        diff_h_prev = diff_h

        # 更新拉格朗日乘子 mu_b^i 和 mu_h^i
        for i in range(n):
            s_b_i_norm = np.linalg.norm(H_approx_x_b.T @ u_b[:, i]) ** 2
            mu_b[i] -= rho * (lambda_b[i] - (1 / K) * s_b_i_norm)
            # print(f"μb的梯度为{(lambda_b[i] - (1 / K) * s_b_i_norm)}")
            s_h_i_norm = np.linalg.norm(H_approx_x_h.T @ u_h[:, i]) ** 2
            mu_h[i] -= rho * (lambda_h[i] - (1 / K) * s_h_i_norm)
            # print(f"μh的梯度为{(lambda_h[i] - (1 / K) * s_h_i_norm)}")
        time_e = time.time()
        print(f"迭代一次总时间：{time_e-time_o}")

    return x_b_recon, x_h_recon, x_b, x_h


from utils.data_parse import RawParse
from method.position import position
from utils.My_utils import *
from scipy import constants
from method.phase_diff_signal import extract_phase_diff_signal

def test():
    file_path = r"D:\PycharmProject\RADAR\HuayiHealthRadar_VSM\data\output\raw\20241213023919.dat"
    # 解析数据
    raw_data = RawParse(file_path)
    print(
        f"Frame Rate: {raw_data.header.frame_rate}  ;Chirp Num Per Frame: {raw_data.header.num_chirps_per_frame}  ;Sample Num Per Chirp: {raw_data.header.num_samples_per_chirp}")
    print(
        f"Range Resolution: {((constants.c / (2 * (raw_data.header.end_frequency_Hz - raw_data.header.start_frequency_Hz))) * 1000):.2f} mm  ;Max Range: {(((constants.c / (2 * (raw_data.header.end_frequency_Hz - raw_data.header.start_frequency_Hz))) / 2) * raw_data.header.num_samples_per_chirp):.2f} m")
    total_frames = raw_data.total_frames - 5  # 减去边界帧
    time_axis = np.arange(total_frames) * (1 / raw_data.header.frame_rate)
    sample_rate = raw_data.header.frame_rate

    # 展示空间定位图
    target_clusters = position(raw_data=raw_data, mti_alpha=0.1,
                                num_beams=27, max_angle=45,
                                eps=5, min_samples=5, energy_threshold=0.995, position_threshold=0.1, plot=False)
    raw_data.file.seek(0)
    horizontal_phase_signals_dict, vertical_phase_signals_dict = extract_phase_diff_signal(
        raw_data=raw_data, target_clusters=target_clusters,
        num_beams=27,  max_angle=45,
        x_offset=1, y_offset=1, z_offset=1)

    # 为每个簇分别计算并绘制水平和垂直相位信号
    for label in horizontal_phase_signals_dict.keys():
        # 打印水平相位信号
        print(f"Processing signals for Target {label} ---------------------")
        horizontal_phase_signals = horizontal_phase_signals_dict[label]

        for i in range(0, horizontal_phase_signals.shape[0], 1):
            # 打印当前两组信号的信息
            for j in range(i, min(i + 1, horizontal_phase_signals.shape[0])):
                print(f"===============================================================我是分割线{j}===============================================================")

                y_t = horizontal_phase_signals[j]
                y_t = normalized_signal(y_t)
                upper_envelope, lower_envelope = envelope(y_t, window_size=20)
                x_b0 = bandpass_filter((upper_envelope + lower_envelope) / 2, 0.1, 0.5, sample_rate, order=1)
                x_h0 = bandpass_filter(y_t - x_b0, 0.8, 2.2, sample_rate, order=1)

                def gradient_process(y_t, n):
                    if n not in [1, 2]:
                        raise ValueError(f"只能是1阶或者2阶，{n}阶引入强烈边界反应不可用")
                    for _ in range(n):
                        # Reflect padding
                        padded_y_t = np.pad(y_t, pad_width=5, mode='reflect')
                        # Calculate gradient and remove padding
                        y_t = np.gradient(padded_y_t)[5:-5]
                    return y_t

                y_dd = bandpass_filter(gradient_process(y_t, n=2), 0.8, 2.2, sample_rate, order=1)
                # 获取 x_h 的最大值和最小值
                x_h_min = np.min(x_h0)
                x_h_max = np.max(x_h0)
                # 将 y_t_dd 归一化到 x_h 的最大值和最小值之间
                y_dd = x_h_min + (y_dd - np.min(y_dd)) * (x_h_max - x_h_min) / (np.max(y_dd) - np.min(y_dd))

                for i in range(50):
                    # x_b_recon, x_h_recon, x_b, x_h = signal_separation(y_t, n=8, alpha_b=1, alpha_h=1, beta=1, gamma=10, rho=0.01, fs=100, max_iter=i+1, tol=1e-6)
                    # x_b_recon, x_h_recon, x_b, x_h = signal_separation(y_t, n=8, alpha_b=1, alpha_h=1, beta=10, gamma=1, rho=0.01, fs=100, max_iter=i+1, tol=1e-6)
                    x_b_recon, x_h_recon, x_b, x_h = signal_separation(y_t, n=4, alpha_b=1, alpha_h=1, beta=1, gamma=10, rho=0.01, fs=100, max_iter=i+1, tol=0.00005)

                    # Organize signals for visualization
                    horizontal_signals = [y_t, x_b0, x_h0, y_dd, x_b_recon, x_h_recon, x_b, x_h]
                    horizontal_titles = ["Original Signal", "1", "2", "3", "4", "5", "6", "7"]
                    # Visualize signals and their FFTs
                    plot_signals_and_ffts(horizontal_signals, horizontal_titles, sample_rate, time_axis)


if __name__ == "__main__":
    test()