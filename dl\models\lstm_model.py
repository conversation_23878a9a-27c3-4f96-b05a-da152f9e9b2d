"""
长短时记忆网络模型定义
"""
import torch
import torch.nn as nn
import torch.nn.functional as F


class SimpleLSTM(nn.Module):
    """简单的LSTM分类模型，用于处理雷达信号序列"""

    def __init__(self, input_size=3, hidden_size=128, num_layers=2, num_classes=10, dropout_rate=0.5):
        super(SimpleLSTM, self).__init__()

        self.input_size = input_size  # 输入特征数量（3个信号：weighted, breathing, heartbeat）
        self.hidden_size = hidden_size  # LSTM隐藏层大小
        self.num_layers = num_layers  # LSTM层数
        self.num_classes = num_classes  # 分类数量

        # LSTM层
        self.lstm = nn.LSTM(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            batch_first=True,  # 输入格式为 (batch, seq_len, input_size)
            dropout=dropout_rate if num_layers > 1 else 0,
            bidirectional=False
        )

        # 注意力机制（可选）
        self.attention = nn.Linear(hidden_size, 1)

        # 全连接层
        self.dropout = nn.Dropout(dropout_rate)
        self.fc1 = nn.Linear(hidden_size, 256)
        self.fc2 = nn.Linear(256, 128)
        self.fc3 = nn.Linear(128, num_classes)

        # 批归一化
        self.bn1 = nn.BatchNorm1d(256)
        self.bn2 = nn.BatchNorm1d(128)

    def forward(self, x):
        """
        前向传播
        输入: x shape = (batch_size, seq_len, input_size)
        输出: 分类结果 shape = (batch_size, num_classes)
        """
        batch_size = x.size(0)

        # 初始化隐藏状态
        h0 = torch.zeros(self.num_layers, batch_size, self.hidden_size).to(x.device)
        c0 = torch.zeros(self.num_layers, batch_size, self.hidden_size).to(x.device)

        # LSTM前向传播
        lstm_out, _ = self.lstm(x, (h0, c0))
        # lstm_out shape: (batch_size, seq_len, hidden_size)

        # 使用注意力机制或简单的最后时刻输出
        if hasattr(self, 'use_attention') and self.use_attention:
            # 注意力机制
            attention_weights = torch.softmax(self.attention(lstm_out), dim=1)
            context_vector = torch.sum(attention_weights * lstm_out, dim=1)
        else:
            # 使用最后一个时刻的输出
            context_vector = lstm_out[:, -1, :]  # shape: (batch_size, hidden_size)

        # 全连接层
        x = F.relu(self.bn1(self.fc1(context_vector)))
        x = self.dropout(x)
        x = F.relu(self.bn2(self.fc2(x)))
        x = self.dropout(x)
        x = self.fc3(x)

        return x

    def get_lstm_features(self, x):
        """获取LSTM特征，用于分析"""
        batch_size = x.size(0)

        h0 = torch.zeros(self.num_layers, batch_size, self.hidden_size).to(x.device)
        c0 = torch.zeros(self.num_layers, batch_size, self.hidden_size).to(x.device)

        lstm_out, (hn, cn) = self.lstm(x, (h0, c0))

        return {
            'lstm_output': lstm_out,  # 所有时刻的输出
            'hidden_state': hn,       # 最终隐藏状态
            'cell_state': cn          # 最终细胞状态
        }


class BiLSTM(nn.Module):
    """双向LSTM分类模型，用于更好地捕获时间序列的双向依赖"""

    def __init__(self, input_size=3, hidden_size=128, num_layers=2, num_classes=10, dropout_rate=0.5):
        super(BiLSTM, self).__init__()

        self.input_size = input_size
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        self.num_classes = num_classes

        # 双向LSTM层
        self.lstm = nn.LSTM(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            batch_first=True,
            dropout=dropout_rate if num_layers > 1 else 0,
            bidirectional=True  # 双向
        )

        # 注意力机制
        self.attention = nn.Linear(hidden_size * 2, 1)  # 双向所以是 hidden_size * 2

        # 全连接层
        self.dropout = nn.Dropout(dropout_rate)
        self.fc1 = nn.Linear(hidden_size * 2, 256)  # 双向输出
        self.fc2 = nn.Linear(256, 128)
        self.fc3 = nn.Linear(128, num_classes)

        self.bn1 = nn.BatchNorm1d(256)
        self.bn2 = nn.BatchNorm1d(128)

    def forward(self, x):
        batch_size = x.size(0)

        # 双向LSTM需要 2 * num_layers 的隐藏状态
        h0 = torch.zeros(self.num_layers * 2, batch_size, self.hidden_size).to(x.device)
        c0 = torch.zeros(self.num_layers * 2, batch_size, self.hidden_size).to(x.device)

        lstm_out, _ = self.lstm(x, (h0, c0))

        # 使用注意力机制
        attention_weights = torch.softmax(self.attention(lstm_out), dim=1)
        context_vector = torch.sum(attention_weights * lstm_out, dim=1)

        # 全连接层
        x = F.relu(self.bn1(self.fc1(context_vector)))
        x = self.dropout(x)
        x = F.relu(self.bn2(self.fc2(x)))
        x = self.dropout(x)
        x = self.fc3(x)

        return x
