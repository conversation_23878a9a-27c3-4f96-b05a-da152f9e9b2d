import numpy as np

class DigitalBeamForming:
    def __init__(self, num_antennas: int, num_beams: int = 27, max_angle_degrees: float = 45, d_by_lambda: float = 0.5):
        """Create a Digital Beam Forming object

        Parameters:
            - num_antennas:         number of (virtual) RX antennas
            - num_beams:            number of beams
            - max_angle_degrees:    maximum angle in degrees, angles will range
                                    from -max_angle_degrees .. +max_angle_degrees
            - d_by_lambda:          separation of RX antennas divided by the wavelength
        """
        angle_vector = np.radians(np.linspace(-max_angle_degrees, max_angle_degrees, num_beams))

        weights = np.zeros((num_antennas, num_beams), dtype=complex)

        for iBeam in range(num_beams):
            angle = angle_vector[iBeam]
            for iAntenna in range(num_antennas):
                weights[iAntenna, iBeam] = np.exp(
                    1j * 2 * np.pi * iAntenna * d_by_lambda * np.sin(angle))  # /sqrt(num_antennas)

        self.weights = weights

    def run(self, range_doppler):
        """Compute virtual beams

        Parameters:
            - range_doppler: Range Doppler spectrum for all RX antennas
              (dimension: num_samples_per_chirp x num_chirps_per_frame x
              num_antennas)

        Returns:
            - Range Doppler Beams (dimension: num_samples_per_chirp x
              num_chirps_per_frame x num_beams)
        """

        num_samples, num_chirps, num_antennas = range_doppler.shape

        num_antennas_internal, num_beams = self.weights.shape

        assert num_antennas == num_antennas_internal

        rd_beam_formed = np.zeros((num_samples, num_chirps, num_beams), dtype=complex);

        for iBeam in range(num_beams):
            acc = np.zeros((num_samples, num_chirps), dtype=complex)

            for iAntenna in range(num_antennas):
                acc += range_doppler[:, :, iAntenna] * self.weights[num_antennas - iAntenna - 1, iBeam]

            rd_beam_formed[:, :, iBeam] = acc

        return rd_beam_formed

def find_max_energy_beam(rd_beam_formed):
    # 计算每个波束的能量
    # 能量计算为每个波束复数值的模的平方和
    beam_energy = np.sum(np.abs(rd_beam_formed)**2, axis=(0, 1))  # shape: (num_beams,)

    # 找到最大能量及其索引
    max_energy_index = np.argmax(beam_energy)
    # max_energy_value = beam_energy[max_energy_index]
    return max_energy_index

def test():
    # 测试参数设置
    num_antennas = 3    # 天线数量
    num_beams = 27   # 波束数量
    max_angle_degrees = 45  # 最大角度
    d_by_lambda = 0.5   # 天线间距与信号波长的比值
    num_samples_per_chirp = 128
    num_chirps_per_frame = 32
    range_doppler = np.random.randn(num_samples_per_chirp, num_chirps_per_frame, num_antennas)
    # 创建 DigitalBeamForming 实例
    dbf = DigitalBeamForming(num_antennas, num_beams, max_angle_degrees, d_by_lambda)
    # 运行波束形成
    result = dbf.run(range_doppler)
    max_energy_index = find_max_energy_beam(result)
    print(max_energy_index)
    # 打印结果的形状
    print("Beamformed data shape:", result.shape)
    # 检查结果是否符合预期的形状
    assert result.shape == (num_samples_per_chirp, num_chirps_per_frame, num_beams), "输出形状不正确！"
    print("测试通过！")

# 执行测试函数
if __name__ == "__main__":
    test()
