import numpy as np
from scipy.ndimage import gaussian_filter1d
from utils.My_utils import *
from method.signal_separation import signal_separation
from utils.utils import merge_signal_dicts, weighted_sum, envelope

def merge_signal_dicts(horizontal_dict, vertical_dict):
    merged_dict = {}
    for key in horizontal_dict.keys():
        # 获取水平和垂直信号
        horizontal_signal = horizontal_dict[key]
        vertical_signal = vertical_dict[key]
        # 在第一个维度上进行拼接
        merged_signal = np.concatenate((horizontal_signal, vertical_signal), axis=0)
        # 将合成后的矩阵存入新字典
        merged_dict[key] = merged_signal
    for key, signal in merged_dict.items():
        # 查找全零的行索引
        zero_rows = np.all(signal == 0, axis=1)
        # 如果某个全零行不是最后一行，则删除
        if any(zero_rows[:-1]):
            # 删除非最后一行且全为零的行
            signal = signal[~(zero_rows & (np.arange(len(zero_rows)) != len(zero_rows) - 1))]
            merged_dict[key] = signal  # 更新merged_dict中对应的信号矩阵

    return merged_dict

def weighted_sum(signals, weights):

    # 确保 weights 长度与 signals 长度相同
    if len(signals) != len(weights):
        raise ValueError("信号和权重的数量不一致")
    # 如果没有有效信号或权重为零，则返回全零数组
    if len(signals) == 0 or sum(weights) == 0:
        # print("No valid signals or total weight is zero, returning zeros.")
        return np.zeros_like(signals[0]) if len(signals) > 0 else np.zeros(1)

    # 加权求和
    total_weight = sum(weights)
    weighted_signal = np.zeros_like(signals[0])
    for signal, weight in zip(signals, weights):
        if np.all(signal == 0):
            print(f"Signal is all zeros, skipping.")
            continue  # 跳过全零信号
        weighted_signal += (signal * weight) / total_weight

    return weighted_signal

def envelope(signal, window_size):
    # 初始化上包络线和下包络线
    upper_envelope = np.zeros_like(signal)
    lower_envelope = np.zeros_like(signal)

    for i in range(0, len(signal), window_size):
        # 获取当前窗口中的信号片段
        window = signal[i:i + window_size]
        if len(window) > 0:
            # 获取窗口内的最大值和最小值
            upper_envelope[i:i + window_size] = np.max(window)
            lower_envelope[i:i + window_size] = np.min(window)

    return upper_envelope, lower_envelope

def vital_signal_ours(horizontal_phase_signals_dict, vertical_phase_signals_dict, sample_rate, bsnr=50, hsnr=32.5):

    phase_signals_dict = merge_signal_dicts(horizontal_phase_signals_dict, vertical_phase_signals_dict)

    final_signals = {}
    for labels, signals in phase_signals_dict.items():
        # 如果该信号只包含一行且全为零，则直接输出同等长度的全零数组
        if signals.shape[0] == 1 and np.all(signals[0] == 0):
            final_signals[labels] = [np.zeros_like(signals[0]), np.zeros_like(signals[0])]
            continue  # 跳过后续处理

        phase_signals = []
        signals_weights = []
        for i in range(signals.shape[0]):

            upper_envelope, lower_envelope = envelope(signals[i], window_size=20)
            mean_envelope = (upper_envelope + lower_envelope) / 2
            breathing_signal = gaussian_filter1d(mean_envelope, sigma=2)
            snr_breathing = SNR(breathing_signal, sample_rate)

            if snr_breathing > bsnr:
                heartbeat_signal = bandpass_filter(signals[i] - mean_envelope, lowcut=0.8, highcut=2.2, fs=sample_rate, order=4)
                snr_heartbeat = SNR(heartbeat_signal, sample_rate)
                if snr_heartbeat > hsnr:
                    phase_signals.append(signals[i])
                    signals_weights.append(snr_heartbeat - hsnr)

        # 如果没有有效信号，跳过处理
        if len(phase_signals) == 0 or sum(signals_weights) == 0:
            final_signals[labels] = [np.zeros_like(signals[0]), np.zeros_like(signals[0])]
            print("Low SNR!!!")
            continue

        # 进行加权求和
        weighted_signal = weighted_sum(phase_signals, signals_weights)
        breathing_signal1, heartbeat_signal1, breathing_signal2, heartbeat_signal2 = signal_separation(weighted_signal, n=5, k=1, alpha_b=1, alpha_h=1, beta=1, gamma=15, rho=0.001, fs=sample_rate, max_iter=30, tol=0.005)
        final_signals[labels] = [breathing_signal1, heartbeat_signal1]

    return weighted_signal, final_signals
