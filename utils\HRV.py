import numpy as np
import matplotlib.pyplot as plt
from scipy.signal import find_peaks
import neurokit2 as nk

def calculate_hrv(heart_rate, heartbeat_signal, sample_rate, time_axis, plot=True):
    # 确保时间轴和信号的长度相同
    min_length = min(len(time_axis), len(heartbeat_signal))
    target_time_axis = time_axis[:min_length]
    heartbeat_signal = heartbeat_signal[:min_length]

    # 根据输入心率估计RR间期（秒）
    rr_interval_estimate = 60 / heart_rate
    # 将RR间期转换为采样点数
    distance_estimate = rr_interval_estimate * sample_rate

    # 使用find_peaks检测R波峰值（这里简单假设心跳信号已类似ECG）
    peaks, _ = find_peaks(heartbeat_signal, distance=distance_estimate/1.4)
    rr_intervals = (np.diff(peaks) / sample_rate) * 1000  # RR间期, 单位ms

    # 在心跳信号图上标记峰值索引
    plt.figure()
    plt.plot(target_time_axis, heartbeat_signal, label='Heartbeat Signal')
    plt.plot(target_time_axis[peaks], heartbeat_signal[peaks], 'ro', label='Peaks')  # 标记峰值
    plt.title('Interpolated Heartbeat Signal with Peaks')
    plt.xlabel('Time (s)')
    plt.ylabel('Amplitude')
    plt.legend()
    if plot:
        plt.show()
    else:
        plt.close()

    # 使用NeuroKit2计算HRV指标
    # 将RR间期与对应时间戳组织为字典
    # RRI是ms，RRI_Time是累计时间（秒）
    rri_time = np.cumsum(rr_intervals) / 1000.0  # 转换为秒
    rri_dict = {"RRI": rr_intervals, "RRI_Time": rri_time}

    # 时域指标
    hrv_time_features = nk.hrv_time(rri_dict, sampling_rate=sample_rate)
    # 频域指标
    hrv_freq_features = nk.hrv_frequency(rri_dict, sampling_rate=sample_rate)


    # 打印部分常用的时域和频域、非线性指标
    # 时域
    mean_rr = hrv_time_features["HRV_MeanNN"][0]    # 平均RR间期
    sdnn = hrv_time_features["HRV_SDNN"][0]         # 标准差SDNN
    rmssd = hrv_time_features["HRV_RMSSD"][0]       # RMSSD
    pnn50 = hrv_time_features["HRV_pNN50"][0]       # pNN50

    print(f"Mean IBI: {mean_rr:.2f} ms")
    print(f"SDRR: {sdnn:.2f} ms")
    print(f"RMSSD: {rmssd:.2f} ms")
    print(f"pNN50: {pnn50:.2f} %")

    # 频域
    lf = hrv_freq_features["HRV_LF"][0]             # 低频功率
    hf = hrv_freq_features["HRV_HF"][0]             # 高频功率
    lf_hf_ratio = hrv_freq_features["HRV_LFHF"][0] * 100  # LF/HF比值

    print(f"LF: {lf:.4f} ms²")
    print(f"HF: {hf:.4f} ms²")
    print(f"LF/HF Ratio: {lf_hf_ratio:.2f}")

    nk.hrv(peaks, sampling_rate=130, show=plot, plot_option=None)


    return rr_intervals, mean_rr, sdnn, rmssd, pnn50
