"""
信号提取脚本
批量处理 .dat 文件，提取 weighted_signal, breathing_signal_norm, heartbeat_signal_norm 信号
"""
import os
import sys
import glob
import json

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

# 导入必要的模块
try:
    from utils.data_parse import RawParse
    from utils.My_utils import normalized_signal
    from method.position import position
    from method.phase_diff_signal import extract_phase_diff_signal
    from method.vital_signals import vital_signal_ours
except ImportError as e:
    print(f"导入模块失败: {e}")
    sys.exit(1)


class SignalExtractor:
    """信号提取器类"""

    def __init__(self, output_dir=None):
        if output_dir is None:
            current_dir = os.path.dirname(os.path.abspath(__file__))
            self.output_dir = os.path.join(current_dir, "extracted_signals")
        else:
            self.output_dir = output_dir

        os.makedirs(self.output_dir, exist_ok=True)

        # 已处理文件记录
        self.processed_files_record = os.path.join(self.output_dir, "processed_files.json")
        self.processed_files = self.load_processed_files()



    def load_processed_files(self):
        """加载已处理文件记录"""
        if os.path.exists(self.processed_files_record):
            try:
                with open(self.processed_files_record, 'r', encoding='utf-8') as f:
                    return set(json.load(f))
            except:
                return set()
        return set()

    def save_processed_files(self):
        """保存已处理文件记录"""
        with open(self.processed_files_record, 'w', encoding='utf-8') as f:
            json.dump(list(self.processed_files), f)

    def is_file_processed(self, file_path):
        """检查文件是否已处理"""
        return os.path.normpath(file_path) in self.processed_files

    def mark_file_processed(self, file_path):
        """标记文件为已处理"""
        self.processed_files.add(os.path.normpath(file_path))
        self.save_processed_files()

    def find_dat_files_in_folders(self, folder_paths):
        """在指定文件夹中查找所有 .dat 文件"""
        dat_files = []
        for folder_path in folder_paths:
            if os.path.exists(folder_path):
                search_pattern = os.path.join(folder_path, "*.dat")
                dat_files.extend(glob.glob(search_pattern))
        return dat_files
    
    def extract_signals_from_file(self, file_path):
        """从单个文件提取信号"""
        try:
            # 解析数据
            raw_data = RawParse(file_path)
            sample_rate = raw_data.header.frame_rate

            # 空间定位
            target_clusters = position(
                raw_data=raw_data,
                mti_alpha=0.1, num_beams=27, max_angle=45,
                eps=5, min_samples=5,
                energy_threshold=0.995, position_threshold=0.1,
                plot=False
            )

            # 重置文件指针
            raw_data.file.seek(0)

            # 提取相位差信号
            horizontal_phase_signals_dict, vertical_phase_signals_dict = extract_phase_diff_signal(
                raw_data=raw_data, target_clusters=target_clusters,
                num_beams=27, max_angle=45,
                x_offset=1, y_offset=1, z_offset=1
            )

            # 提取生命信号
            weighted_signal, final_signals = vital_signal_ours(
                horizontal_phase_signals_dict, vertical_phase_signals_dict,
                sample_rate=sample_rate, bsnr=40, hsnr=25
            )

            # 归一化加权信号
            weighted_signal_norm = normalized_signal(weighted_signal) if weighted_signal is not None else None

            # 构建结果
            file_results = {
                'file_name': os.path.basename(file_path),
                'weighted_signal_norm': [float(x) for x in weighted_signal_norm] if weighted_signal_norm is not None else None,
                'clusters': {}
            }

            # 处理每个簇
            for label in final_signals.keys():
                label_str = str(label)
                breathing_signal = final_signals[label][0]
                heartbeat_signal = final_signals[label][1]
                breathing_signal_norm = normalized_signal(breathing_signal)
                heartbeat_signal_norm = normalized_signal(heartbeat_signal)

                file_results['clusters'][label_str] = {
                    'breathing_signal_norm': [float(x) for x in breathing_signal_norm],
                    'heartbeat_signal_norm': [float(x) for x in heartbeat_signal_norm]
                }

            return file_results

        except Exception as e:
            return {
                'file_name': os.path.basename(file_path),
                'error': str(e)
            }

    def save_single_file_result(self, result):
        """保存单个文件的处理结果"""
        if 'error' in result:
            return False

        try:
            file_name = result['file_name'].replace('.dat', '')
            output_file = os.path.join(self.output_dir, f"signal_{file_name}.json")

            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"❌ 保存失败: {e}")
            return False

    def process_folders(self, folder_paths, skip_processed=True):
        """处理指定文件夹中的所有 .dat 文件"""
        all_dat_files = self.find_dat_files_in_folders(folder_paths)

        if not all_dat_files:
            return []

        # 过滤已处理的文件
        if skip_processed:
            dat_files = [f for f in all_dat_files if not self.is_file_processed(f)]
        else:
            dat_files = all_dat_files

        if not dat_files:
            return []

        for i, file_path in enumerate(dat_files, 1):
            print(f"处理 {i}/{len(dat_files)}: {os.path.basename(file_path)}")

            result = self.extract_signals_from_file(file_path)

            if 'error' not in result:
                # 只有保存成功后才标记为已处理
                if self.save_single_file_result(result):
                    self.mark_file_processed(file_path)
                    print(f"✅ 完成")
                else:
                    print(f"❌ 保存失败")
            else:
                print(f"❌ 处理失败: {result['error']}")

        return dat_files


def main():
    """主函数"""
    folder_paths = [
        # r"D:\PycharmProject\RadarDL\data\.User\U1-CGY\无压力",
        # r"D:\PycharmProject\RadarDL\data\.User\U1-CGY\生理压力",
        # r"D:\PycharmProject\RadarDL\data\.User\U2-CWX",
        # r"D:\PycharmProject\RadarDL\data\.User\U3-DXH",
        # r"D:\PycharmProject\RadarDL\data\.User\U4",
        # r"D:\PycharmProject\RadarDL\data\.User\U5",
        # r"D:\PycharmProject\RadarDL\data\.User\U6-GYN",
        # r"D:\PycharmProject\RadarDL\data\.User\U6-LXF",
        # r"D:\PycharmProject\RadarDL\data\.Angele0\radar",
        # r"D:\PycharmProject\RadarDL\data\.Angele15\radar",
        # r"D:\PycharmProject\RadarDL\data\.Angele30\radar",
        # r"D:\PycharmProject\RadarDL\data\.Range30\radar",
        # r"D:\PycharmProject\RadarDL\data\.Range50\radar",
        # r"D:\PycharmProject\RadarDL\data\.Range75\radar",
        # r"D:\PycharmProject\RadarDL\data\.Range100\radar",
        r"D:\PycharmProject\RadarDL\data\2",
    ]

    extractor = SignalExtractor()
    all_files = extractor.find_dat_files_in_folders(folder_paths)
    processed_count = sum(1 for f in all_files if extractor.is_file_processed(f))

    print(f"总文件: {len(all_files)}, 已处理: {processed_count}, 待处理: {len(all_files) - processed_count}")

    if processed_count == len(all_files):
        print("所有文件已处理完成")
        return

    extractor.process_folders(folder_paths, skip_processed=True)
    print("处理完成")


def reset_processed_files():
    """重置已处理文件记录"""
    extractor = SignalExtractor()
    if os.path.exists(extractor.processed_files_record):
        os.remove(extractor.processed_files_record)
        print("已处理文件记录已重置")
    else:
        print("没有找到已处理文件记录")

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "--reset":
        reset_processed_files()
    else:
        main()
