# 公共函数模块
import numpy as np

def merge_signal_dicts(horizontal_dict, vertical_dict):
    merged_dict = {}
    for key in horizontal_dict.keys():
        horizontal_signal = horizontal_dict[key]
        vertical_signal = vertical_dict[key]
        merged_signal = np.concatenate((horizontal_signal, vertical_signal), axis=0)
        merged_dict[key] = merged_signal
    for key, signal in merged_dict.items():
        zero_rows = np.all(signal == 0, axis=1)
        if any(zero_rows[:-1]):
            signal = signal[~(zero_rows & (np.arange(len(zero_rows)) != len(zero_rows) - 1))]
            merged_dict[key] = signal
    return merged_dict

def weighted_sum(signals, weights):
    if len(signals) != len(weights):
        raise ValueError("信号和权重的数量不一致")
    if len(signals) == 0 or sum(weights) == 0:
        return np.zeros_like(signals[0]) if len(signals) > 0 else np.zeros(1)
    total_weight = sum(weights)
    weighted_signal = np.zeros_like(signals[0])
    for signal, weight in zip(signals, weights):
        if np.all(signal == 0):
            continue
        weighted_signal += (signal * weight) / total_weight
    return weighted_signal

def envelope(signal, window_size):
    upper_envelope = np.zeros_like(signal)
    lower_envelope = np.zeros_like(signal)
    for i in range(0, len(signal), window_size):
        window = signal[i:i + window_size]
        if len(window) > 0:
            upper_envelope[i:i + window_size] = np.max(window)
            lower_envelope[i:i + window_size] = np.min(window)
    return upper_envelope, lower_envelope 