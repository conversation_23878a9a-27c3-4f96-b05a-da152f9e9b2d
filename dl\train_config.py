"""
训练配置文件
可以通过修改这个文件来调整训练参数
"""
import os
import torch


class TrainConfig:
    """训练配置类"""
    
    # ==================== 数据相关配置 ====================
    DATA_DIR = "data"  # 数据目录（相对于dl目录）
    SIGNAL_TYPE = "heartbeat_signal_norm"  # 信号类型
    SEQUENCE_LENGTH = 1024  # 序列长度（改为1024以匹配新的数据格式）
    BATCH_SIZE = 2  # 批次大小（减小批次，数据量小时更稳定）
    NUM_WORKERS = 4  # 数据加载工作进程数
    VAL_SPLIT = 0.1  # 验证集比例（减少验证集，增加训练数据）
    TEST_SPLIT = 0.0  # 测试集比例（设为0，只使用训练集和验证集）
    
    # ==================== 模型相关配置 ====================
    MODEL_NAME = "SimpleLSTM"
    INPUT_SIZE = 1  # LSTM输入特征数（单个信号）
    HIDDEN_SIZE = 64  # LSTM隐藏层大小（减小模型复杂度，防止过拟合）
    NUM_LAYERS = 1  # LSTM层数（减少层数，简化模型）
    NUM_CLASSES = 3  # 分类数量（标签0、标签1和标签2）
    DROPOUT_RATE = 0.1  # Dropout比例（减少Dropout，数据量小时过度正则化有害）
    BIDIRECTIONAL = False  # 是否使用双向LSTM
    
    # ==================== 训练相关配置 ====================
    START_EPOCH = 1  # 开始轮数（从第几轮开始训练）
    EPOCHS = 200  # 总训练轮数（增加训练轮数）
    LEARNING_RATE = 0.001  # 初始学习率（提高学习率）
    WEIGHT_DECAY = 1e-6  # 权重衰减（减少正则化）
    PATIENCE = 30  # 早停耐心值（增加耐心值）

    # ==================== 权重加载配置 ====================
    RESUME_TRAINING = False  # 是否从检查点继续训练
    CHECKPOINT_PATH = ""  # 检查点文件路径（如果要继续训练，设置此路径）
    LOAD_OPTIMIZER = True  # 是否加载优化器状态（继续训练时建议为True）
    
    # ==================== 优化器配置 ====================
    OPTIMIZER = "Adam"  # 优化器类型 ("Adam", "SGD", "AdamW")
    MOMENTUM = 0.9  # SGD动量（仅当使用SGD时有效）
    BETAS = (0.9, 0.999)  # Adam的beta参数
    
    # ==================== 学习率调度配置 ====================
    USE_SCHEDULER = True  # 是否使用学习率调度器
    SCHEDULER_TYPE = "ReduceLROnPlateau"  # 调度器类型
    SCHEDULER_FACTOR = 0.6  # 学习率衰减因子（减缓衰减）
    SCHEDULER_PATIENCE = 15  # 调度器耐心值（增加耐心值）
    SCHEDULER_MIN_LR = 1e-7  # 最小学习率（提高最小学习率）
    
    # ==================== 设备配置 ====================
    DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # ==================== 文件路径配置 ====================
    MODEL_SAVE_PATH = "checkpoints"  # 模型保存路径（相对于dl目录）
    LOG_DIR = "logs"  # 日志目录（相对于dl目录）
    
    # ==================== 数据增强配置 ====================
    USE_AUGMENTATION = False  # 是否使用数据增强
    NOISE_FACTOR = 0.01  # 噪声因子
    
    # ==================== 其他配置 ====================
    RANDOM_SEED = 42  # 随机种子
    PRINT_FREQ = 1  # 打印频率（每多少个epoch打印一次）
    SAVE_FREQ = 10  # 保存频率（每多少个epoch保存一次检查点）
    
    # ==================== 验证配置 ====================
    @classmethod
    def validate_config(cls):
        """验证配置参数的合理性"""
        errors = []
        
        # 检查基本参数
        if cls.BATCH_SIZE <= 0:
            errors.append("BATCH_SIZE 必须大于 0")
        
        if cls.LEARNING_RATE <= 0:
            errors.append("LEARNING_RATE 必须大于 0")
        
        if cls.EPOCHS <= 0:
            errors.append("EPOCHS 必须大于 0")

        if cls.START_EPOCH <= 0:
            errors.append("START_EPOCH 必须大于 0")

        if cls.START_EPOCH > cls.EPOCHS:
            errors.append("START_EPOCH 不能大于 EPOCHS")

        # 检查检查点配置
        if cls.RESUME_TRAINING and not cls.CHECKPOINT_PATH:
            errors.append("启用继续训练时必须设置 CHECKPOINT_PATH")

        if cls.CHECKPOINT_PATH and not os.path.exists(cls.CHECKPOINT_PATH) and cls.RESUME_TRAINING:
            errors.append(f"检查点文件不存在: {cls.CHECKPOINT_PATH}")
        
        if cls.HIDDEN_SIZE <= 0:
            errors.append("HIDDEN_SIZE 必须大于 0")
        
        if cls.NUM_LAYERS <= 0:
            errors.append("NUM_LAYERS 必须大于 0")
        
        if not 0 <= cls.DROPOUT_RATE < 1:
            errors.append("DROPOUT_RATE 必须在 [0, 1) 范围内")
        
        if not 0 < cls.VAL_SPLIT < 1:
            errors.append("VAL_SPLIT 必须在 (0, 1) 范围内")
        
        if cls.VAL_SPLIT + cls.TEST_SPLIT >= 1:
            errors.append("VAL_SPLIT + TEST_SPLIT 必须小于 1")
        
        # 检查优化器
        if cls.OPTIMIZER not in ["Adam", "SGD", "AdamW"]:
            errors.append("OPTIMIZER 必须是 'Adam', 'SGD' 或 'AdamW' 之一")
        
        # 检查调度器
        if cls.SCHEDULER_TYPE not in ["ReduceLROnPlateau", "StepLR", "CosineAnnealingLR"]:
            errors.append("SCHEDULER_TYPE 必须是支持的调度器类型")
        
        if errors:
            raise ValueError("配置错误:\n" + "\n".join(f"- {error}" for error in errors))
        
        return True
    
    @classmethod
    def print_config(cls):
        """打印当前配置"""
        print("=" * 80)
        print("训练配置")
        print("=" * 80)
        
        print("📊 数据配置:")
        print(f"   数据目录: {cls.DATA_DIR}")
        print(f"   信号类型: {cls.SIGNAL_TYPE}")
        print(f"   序列长度: {cls.SEQUENCE_LENGTH}")
        print(f"   批次大小: {cls.BATCH_SIZE}")
        print(f"   验证集比例: {cls.VAL_SPLIT}")
        print(f"   测试集比例: {cls.TEST_SPLIT}")
        
        print("\n🧠 模型配置:")
        print(f"   模型名称: {cls.MODEL_NAME}")
        print(f"   输入特征数: {cls.INPUT_SIZE}")
        print(f"   隐藏层大小: {cls.HIDDEN_SIZE}")
        print(f"   LSTM层数: {cls.NUM_LAYERS}")
        print(f"   分类数量: {cls.NUM_CLASSES}")
        print(f"   Dropout率: {cls.DROPOUT_RATE}")
        print(f"   双向LSTM: {cls.BIDIRECTIONAL}")
        
        print("\n🏃 训练配置:")
        print(f"   开始轮数: {cls.START_EPOCH}")
        print(f"   总训练轮数: {cls.EPOCHS}")
        print(f"   学习率: {cls.LEARNING_RATE}")
        print(f"   权重衰减: {cls.WEIGHT_DECAY}")
        print(f"   早停耐心值: {cls.PATIENCE}")
        print(f"   优化器: {cls.OPTIMIZER}")

        print("\n📂 权重加载配置:")
        print(f"   继续训练: {cls.RESUME_TRAINING}")
        if cls.RESUME_TRAINING and cls.CHECKPOINT_PATH:
            print(f"   检查点路径: {cls.CHECKPOINT_PATH}")
            print(f"   加载优化器: {cls.LOAD_OPTIMIZER}")
        else:
            print(f"   从头开始训练")
        
        print("\n📱 设备配置:")
        print(f"   设备: {cls.DEVICE}")
        
        print("\n💾 路径配置:")
        print(f"   模型保存路径: {cls.MODEL_SAVE_PATH}")
        print(f"   日志目录: {cls.LOG_DIR}")
        
        print("=" * 80)


# 使用示例：
# 1. 直接修改上面的类属性来调整参数
# 2. 或者在训练脚本中动态修改：
#    TrainConfig.LEARNING_RATE = 0.0005
#    TrainConfig.BATCH_SIZE = 32
# 3. 验证配置：
#    TrainConfig.validate_config()


if __name__ == "__main__":
    # 验证并打印配置
    TrainConfig.validate_config()
    TrainConfig.print_config()
