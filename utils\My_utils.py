import os.path
import numpy as np
import matplotlib.pyplot as plt
from scipy import stats
from scipy.signal import iirfilter, filtfilt
from scipy.ndimage import uniform_filter1d
import math
import time


def fft_spectrum(mat, range_window):
    """"
    计算 FFT 频谱
    mat:FMCW 雷达的 chirp 数据. 矩阵形式:[num_chirps, num_samples]
    range_window: 在进行 FFT 之前应用于输入数据的窗口函数
    """
    # -------------------------------------------------
    # 步骤 1 - 移除样本中的直流偏置
    # -------------------------------------------------
    [num_chirps, num_samples] = np.shape(mat)
    # 计算每行（chirp）的平均值
    avgs = np.average(mat, 1).reshape(num_chirps, 1)
    # 去偏值
    mat = mat - avgs

    # -------------------------------------------------
    # 步骤 2 - 对数据应用窗口函数
    # -------------------------------------------------
    mat = np.multiply(mat, range_window)

    # -------------------------------------------------
    # 步骤 3 - 添加零填充
    # -------------------------------------------------
    zp1 = np.pad(mat, ((0, 0), (0, num_samples)), 'constant')

    # -------------------------------------------------
    # 步骤 4 - 计算用于距离信息的 FFT
    # -------------------------------------------------
    range_fft = np.fft.fft(zp1) / num_samples
    # 忽略负频谱中的冗余信息，并通过加倍幅度来补偿能量
    range_fft = 2 * range_fft[:, range(int(num_samples))]

    return range_fft


# 带通滤波器
def bandpass_filter(data, lowcut, highcut, fs, order=3):
    nyquist = 0.5 * fs
    low = lowcut / nyquist
    high = highcut / nyquist
    b, a = iirfilter(order, [low, high], btype='band', ftype='butter')
    y = filtfilt(b, a, data)
    return y


# 计算FFT
def calculate_fft(signal, sample_rate):
    length = len(signal)
    range_window = np.hamming(length)
    signal_spectrum = fft_spectrum(signal.reshape(1, -1), range_window).flatten()
    signal_spectrum = np.abs(signal_spectrum)
    freqs = np.fft.fftfreq(2*length, d=1 / sample_rate)[:length]

    return signal_spectrum, freqs


# 展示信号和频谱图
def plot_signals_and_ffts(signals, titles, sample_rate, time_axis, freq_range=(-0.1, 2.5), save_path=None, ifsave=False):
    num_signals = len(signals)
    fig, axes = plt.subplots(num_signals, 2, figsize=(20, 5 * num_signals))
    if num_signals == 1:
        axes = np.expand_dims(axes, axis=0)
    for i, (signal, title) in enumerate(zip(signals, titles)):
        signal_spectrum, freqs = calculate_fft(signal, sample_rate)

        # 原始信号
        axes[i, 0].plot(time_axis[:len(signal)], signal)
        axes[i, 0].set_title(f'{title} - Signal')
        axes[i, 0].set_xlabel('Sample Index')
        axes[i, 0].set_ylabel('Amplitude')

        # FFT后的信号
        axes[i, 1].plot(freqs, signal_spectrum)
        axes[i, 1].set_xlabel('Frequency (Hz)')
        axes[i, 1].set_ylabel('Amplitude')
        axes[i, 1].set_title(f'{title} - FFT')
        axes[i, 1].set_xlim(freq_range)

    if ifsave and save_path:
        plt.savefig(os.path.join(save_path, "signals_and_ffts.png"))
        print(f"Figure saved to {save_path}")

    plt.tight_layout()

    plt.show()


# 计算参数展示信号频谱图
def plot_rate(signal, sample_rate, plot=False, title=None, ifsave=False, save_path=None):
    # 计算FFT
    signal_spectrum, freqs = calculate_fft(signal, sample_rate)
    # 找到峰值
    peak_idx = np.argmax(signal_spectrum)
    peak_freq = freqs[peak_idx]
    peak_amplitude = signal_spectrum[peak_idx]
    # 计算参数
    rate = math.ceil(peak_freq * 60)  # 转换为次/分钟并向上取整

    if plot == True:
        # 展示频谱图
        plt.figure()
        plt.plot(freqs, signal_spectrum)
        plt.plot(peak_freq, peak_amplitude, 'ro')  # 标出峰值
        plt.annotate(f'Peak Frequency = {peak_freq:.2f} Hz\n{title} Rate = {rate:.2f} BPM',
                     xy=(peak_freq, peak_amplitude),
                     xytext=(peak_freq + 0.05, peak_amplitude * 1.05),
                     arrowprops=dict(facecolor='black', arrowstyle='->'),
                     bbox=dict(boxstyle="round,pad=0.3", edgecolor='black', facecolor='white'))
        plt.title(f'{title} Signal Spectrum')
        plt.xlabel('Frequency (Hz)')
        plt.ylabel('Amplitude')
        plt.xlim(-0.1, 5)
        if ifsave and save_path:
            plt.savefig(save_path)
        plt.show()
        print(f"{title} Rate = ", rate)

    return rate, peak_freq, peak_amplitude


# 归一化信号
def normalized_signal(signal, method="min_max"):
    # 可选参数：smooth, min_max, z_score, max_abs, decimal_scaling, mean
    if method == "smooth":
        # 对原始信号进行移动平均滤波
        filtered_siganl = uniform_filter1d(signal, size=2)
        # 提取原始信号的幅值
        signal_abs = np.abs(signal)
        # 对信号幅值进行移动平均滤波
        smoothed_signal = uniform_filter1d(signal_abs, size=2)
        # 归一化：归一化后的信号是平滑后的原始信号和幅值之间的比率
        normalized_heart_data = filtered_siganl / smoothed_signal
        return normalized_heart_data
    elif method =="min_max":
        return (signal - np.min(signal)) / (np.max(signal) - np.min(signal))
    elif method == 'z_score':
        return (signal - np.mean(signal)) / np.std(signal)
    elif method == 'max_abs':
        return signal / np.max(np.abs(signal))
    elif method == 'decimal_scaling':
        j = np.ceil(np.log10(np.max(np.abs(signal))))
        return signal / 10**j
    elif method == 'mean':
        return (signal - np.mean(signal)) / (np.max(signal) - np.min(signal))
    else:
        raise ValueError("Unsupported normalization method")


def ICA(signal, confidence_level=0.97, mode='soft'):
    """
    Iterative_Confidence_Adjustment
    计算一维信号的置信区间，并根据模式进行值的处理。

    参数:
    signal: 输入的一维信号 (numpy array or list)
    confidence_level: 置信度水平, 默认为0.97
    mode: 处理模式，"soft"表示将超过置信区间的值设置为边界值，
          "hard"表示将超过置信区间的值设置为0。

    返回:
    modified_signal: 修改后的信号，所有值都在置信区间范围内
    """
    start_time = time.time()

    signal = np.array(signal)  # 确保信号为numpy数组
    z_value = stats.norm.ppf((1 + confidence_level) / 2)  # 提前计算Z值
    iteration_count = 0  # 记录循环次数
    total_processed_values = 0  # 记录处理的值数量

    while True:
        iteration_count += 1

        # Step 1: 计算信号的均值和标准差
        mean = np.mean(signal)
        std_dev = np.std(signal)  # 使用 NumPy 的 std 更快
        lower_bound = mean - z_value * std_dev
        upper_bound = mean + z_value * std_dev

        # Step 2: 查找超过置信区间的值
        outside_indices = np.where((signal < lower_bound) | (signal > upper_bound))[0]
        total_processed_values += len(outside_indices)

        # Step 3: 如果没有值在置信区间外，则停止
        if len(outside_indices) == 0:
            break

        # Step 4: 处理超过置信区间的值
        if mode == 'soft':
            signal = np.clip(signal, lower_bound, upper_bound)  # 使用 NumPy 的 clip 函数更高效
        elif mode == 'hard':
            signal[outside_indices] = 0
        else:
            raise ValueError("模式应该是 'soft' 或 'hard'")

    end_time = time.time()
    print(f"循环次数: {iteration_count}----------------处理的值总数: {total_processed_values}----------------离群算法估计时间: {end_time - start_time:.6f} 秒")

    return signal

def SNR(signal, sample_rate):
    # 计算 FFT 并获取频谱
    signal_spectrum, _ = calculate_fft(signal, sample_rate)

    # 找到主峰值的幅度
    peak_amplitude = np.max(signal_spectrum)

    # 计算噪声基准：去除主峰值后的其余部分取中位数
    noise_mask = signal_spectrum < peak_amplitude  # 使用掩码
    noise_amplitude = np.mean(signal_spectrum[noise_mask])
    # 计算信噪比 (线性形式)
    snr = peak_amplitude / noise_amplitude

    # 返回 dB 形式的信噪比
    snr_db = 20 * np.log10(snr)
    return snr_db

def envelope(signal, window_size):
    # 初始化上包络线和下包络线
    upper_envelope = np.zeros_like(signal)
    lower_envelope = np.zeros_like(signal)

    for i in range(0, len(signal), window_size):
        # 获取当前窗口中的信号片段
        window = signal[i:i + window_size]
        if len(window) > 0:
            # 获取窗口内的最大值和最小值
            upper_envelope[i:i + window_size] = np.max(window)
            lower_envelope[i:i + window_size] = np.min(window)

    return upper_envelope, lower_envelope