import numpy as np
import matplotlib.pyplot as plt
from scipy.signal import find_peaks
from scipy.linalg import eigh
from utils.My_utils import *
import time
def MUSIC(signal, fs, num_sources=2, nfft=1024, plot=False):
    """
    使用 MUSIC 算法估计频率。

    Parameters:
    signal: 1D numpy 数组，输入信号
    fs: 采样频率
    num_sources: 要估计的信号源数量
    nfft: 计算 MUSIC 频谱时的频率点数

    Returns:
    freqs: 频谱的计算频率
    spectrum: MUSIC 频谱
    estimated_freqs: 估计出的频率s
    """
    # Parameters
    start_time = time.time()
    signal_length = len(signal)
    M = signal_length // 2  # Hankel 矩阵的行数
    N = signal_length - M + 1  # Hankel 矩阵的列数

    # Efficient Hankel matrix construction
    indices = np.arange(M)[:, None] + np.arange(N)
    hankel_matrix = signal[indices]

    # 计算协方差矩阵
    R = np.dot(hankel_matrix, hankel_matrix.conj().T) / N

    # 特征值分解 using eigh for Hermitian matrices
    eigenvalues, eigenvectors = eigh(R)
    # Reverse the order to get descending eigenvalues
    # eigenvalues = eigenvalues[::-1]
    eigenvectors = eigenvectors[:, ::-1]

    # 噪声子空间
    En = eigenvectors[:, num_sources:]
    En_H = En.conj().T  # Precompute for efficiency

    # 计算 MUSIC 频谱
    freqs = np.linspace(0, fs / 2, nfft)
    M_range = np.arange(M)
    steering_vectors = np.exp(-1j * 2 * np.pi * freqs[:, np.newaxis] * M_range / fs)

    # Compute the spectrum efficiently without a loop
    En_steering = En_H @ steering_vectors.T  # Shape: (M - num_sources) x nfft
    numerator = np.linalg.norm(En_steering, axis=0) ** 2
    spectrum = 1 / numerator

    # 归一化频谱
    spectrum = 10 * np.log10(spectrum / np.max(spectrum))

    # 在频谱中寻找峰值
    peaks, _ = find_peaks(spectrum)  # Find all peaks
    peak_values = spectrum[peaks]

    if len(peak_values) > 0:
        # # 选择峰值数值超过一半的峰值
        # threshold = (np.min(peak_values) + np.max(peak_values)) / 2
        # significant_peaks = peaks[peak_values >= threshold]
        # estimated_freqs = freqs[significant_peaks]
        # 找到最大的峰值及其对应频率
        max_peak_index = np.argmax(peak_values)  # 最大峰值的索引
        significant_peaks = peaks[max_peak_index]  # 最大峰值在频率数组中的位置
        estimated_freqs = np.array([freqs[significant_peaks]])  # 返回最大峰值对应的频率
    else:
        print("未找到任何峰值")
        estimated_freqs = np.array([])  # 返回空数组

    end_time = time.time()
    if plot:
        # Plot the original signal and its FFT in one figure
        plt.figure(figsize=(12, 8))
        # 绘制 MUSIC 频谱
        plt.plot(freqs, spectrum, label='MUSIC Spectrum')
        # 标记峰值
        if len(estimated_freqs) > 0:
            plt.plot(estimated_freqs, spectrum[significant_peaks], 'ro', label='Peaks')
            for freq in estimated_freqs:
                if freq <= 5:
                    plt.text(freq, spectrum[np.where(freqs == freq)[0][0]], f'{freq*60:.2f} Hz', fontsize=10, color='red')
        plt.xlim(0, 5)
        plt.title('MUSIC Spectrum (0-5 Hz)')
        plt.xlabel('Frequency (Hz)')
        plt.ylabel('Spectrum (dB)')
        plt.grid(True)
        plt.legend()
        plt.show()
    print(f"MUSIC算法估计时间: {end_time - start_time:.6f} 秒")
    return freqs, spectrum, estimated_freqs

from scipy.ndimage import  gaussian_filter

def main():
    # 生成模拟数据
    fs = 20  # 采样频率
    t = np.arange(0, 30, 1 / fs)  # 30 秒时间
    breathing_freq = 0.5  # 实际呼吸信号频率 (Hz)
    heartbeat_freq = 1.25  # 实际心跳信号频率 (Hz)

    signal = np.sin(2 * np.pi * breathing_freq * t) + \
             0.21 * np.sin(2 * np.pi * heartbeat_freq * t) + \
             2 * np.random.randn(len(t))
    signal1 = gaussian_filter(signal, sigma=2)
    signal2 = gaussian_filter(calculate_confidence_interval(signal1, confidence_level=0.97, mode='soft'), sigma=2)
    signal3 = gaussian_filter(calculate_confidence_interval(signal1, confidence_level=0.97, mode='hard'), sigma=2)

    # 可视化
    time_axis = np.arange(600)
    signals = [signal, signal1, signal2, signal3]
    titles = ["signal", "signal_filter", "denoised_signal", "denoised_signal"]
    plot_signals_and_ffts(signals, titles, fs, time_axis)

    # 使用 MUSIC 算法估计频率
    MUSIC(signal, fs, num_sources=2, plot=True)
    MUSIC(signal1, fs, num_sources=2, plot=True)
    MUSIC(signal2, fs, num_sources=2, plot=True)
    MUSIC(signal3, fs, num_sources=2, plot=True)

    # print("估计的频率 (Hz):", estimated_freqs)

if __name__ == "__main__":
    main()