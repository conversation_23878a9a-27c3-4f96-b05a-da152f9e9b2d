"""
批量处理信号文件，将长信号按1024长度切分成多个文件
"""
import os
import json
import glob
import numpy as np


def split_signal_file(input_file, output_dir, label, chunk_size=1024):
    """
    分割单个信号文件
    
    Args:
        input_file: 输入JSON文件路径
        output_dir: 输出目录
        label: 标签（用于命名）
        chunk_size: 每个块的大小，默认1024
    
    Returns:
        生成的文件数量
    """
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 提取heartbeat_signal_norm信号
        signal = None
        if 'clusters' in data and '0' in data['clusters']:
            cluster_data = data['clusters']['0']
            if 'heartbeat_signal_norm' in cluster_data:
                signal = np.array(cluster_data['heartbeat_signal_norm'], dtype=np.float32)
        elif 'heartbeat_signal_norm' in data:
            signal = np.array(data['heartbeat_signal_norm'], dtype=np.float32)
        
        if signal is None:
            print(f"  ❌ 未找到heartbeat_signal_norm信号: {os.path.basename(input_file)}")
            return 0
        
        # 计算可以分割的块数
        num_chunks = len(signal) // chunk_size
        if num_chunks == 0:
            print(f"  ⚠️  信号长度不足{chunk_size}: {os.path.basename(input_file)} (长度: {len(signal)})")
            return 0
        
        # 分割并保存
        saved_count = 0
        for i in range(num_chunks):
            start_idx = i * chunk_size
            end_idx = start_idx + chunk_size
            chunk_signal = signal[start_idx:end_idx].tolist()
            
            # 创建新的JSON结构
            new_data = {
                "file_name": f"split_{os.path.basename(input_file)}_{i+1}",
                "clusters": {
                    "0": {
                        "heartbeat_signal_norm": chunk_signal
                    }
                }
            }
            
            # 生成输出文件名
            base_name = os.path.splitext(os.path.basename(input_file))[0]
            output_file = os.path.join(output_dir, f"{base_name}_chunk_{i+1}.json")
            
            # 保存文件
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(new_data, f, indent=2)
            
            saved_count += 1
        
        print(f"  ✅ {os.path.basename(input_file)}: 原长度{len(signal)} -> 分割成{saved_count}个文件")
        return saved_count
        
    except Exception as e:
        print(f"  ❌ 处理文件失败: {os.path.basename(input_file)} - {e}")
        return 0


def process_label_folder(input_dir, output_dir, label):
    """
    处理单个标签文件夹
    
    Args:
        input_dir: 输入目录 (如 dl/data/data/0)
        output_dir: 输出目录 (如 dl/data/0)
        label: 标签值
    
    Returns:
        处理的文件数和生成的文件数
    """
    if not os.path.exists(input_dir):
        print(f"❌ 输入目录不存在: {input_dir}")
        return 0, 0
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 查找所有JSON文件
    json_files = glob.glob(os.path.join(input_dir, "*.json"))
    
    if not json_files:
        print(f"❌ 未找到JSON文件: {input_dir}")
        return 0, 0
    
    print(f"📁 处理标签 {label}: 找到 {len(json_files)} 个文件")
    
    total_processed = 0
    total_generated = 0
    
    for json_file in json_files:
        generated_count = split_signal_file(json_file, output_dir, label)
        if generated_count > 0:
            total_processed += 1
            total_generated += generated_count
    
    print(f"📊 标签 {label} 完成: 处理 {total_processed}/{len(json_files)} 个文件，生成 {total_generated} 个新文件")
    return total_processed, total_generated


def main():
    """主函数"""
    print("🚀 批量分割信号文件")
    print("=" * 60)
    
    # 配置路径
    base_dir = "."  # 当前目录 (dl/data)
    input_base = os.path.join(base_dir, "data")  # dl/data/data
    
    # 处理的标签列表
    labels = ["2"]
    
    total_processed_files = 0
    total_generated_files = 0
    
    for label in labels:
        input_dir = os.path.join(input_base, label)  # dl/data/data/0 或 dl/data/data/1
        output_dir = os.path.join(base_dir, label)   # dl/data/0 或 dl/data/1
        
        processed, generated = process_label_folder(input_dir, output_dir, label)
        total_processed_files += processed
        total_generated_files += generated
        print()
    
    print("=" * 60)
    print("📊 处理完成")
    print("=" * 60)
    print(f"总处理文件数: {total_processed_files}")
    print(f"总生成文件数: {total_generated_files}")
    print(f"平均每个文件生成: {total_generated_files/total_processed_files:.1f} 个新文件" if total_processed_files > 0 else "无文件处理")


if __name__ == "__main__":
    main()
