from utils.DigitalBeamForming import DigitalBeamForming
from utils.DopplerAlgo import DopplerAlgo
from utils.data_parse import RawParse

import numpy as np
from scipy import constants
import matplotlib.pyplot as plt
from collections import Counter
from sklearn.cluster import DBSCAN
# np.set_printoptions(threshold=np.inf)

def plot_max_energy_coordinates(cluster_coordinates, x_max, y_max, z_max, num_ellipses=5):
    fig = plt.figure()
    ax = fig.add_subplot(111, projection='3d')

    # 使用 'tab10' 调色盘，不传递额外参数
    colors = plt.get_cmap('tab10')

    # 遍历不同类别，并绘制目标的3D坐标点
    for label, coordinates_list in cluster_coordinates.items():
        x_coords = [coord[0] for coord in coordinates_list]
        y_coords = [coord[1] for coord in coordinates_list]
        z_coords = [coord[2] for coord in coordinates_list]

        ax.scatter(x_coords, y_coords, z_coords, color=colors(label % 10), marker='o', label=f'Target {label}')

    # 隐藏坐标轴
    ax.set_axis_off()

    # 绘制相交的坐标轴线
    ax.plot([-x_max / 2, x_max / 2], [0, 0], [0, 0], color='k', lw=1)  # x 轴
    ax.plot([0, 0], [0, y_max], [0, 0], color='k', lw=1)  # y 轴 (仅正方向)
    ax.plot([0, 0], [0, 0], [-z_max / 2, z_max / 2], color='k', lw=1)  # z 轴

    # 在坐标轴线附近标注
    ax.text(x_max / 2, 0, 0, "X", color='black', ha='center', va='center')
    ax.text(0, y_max, 0, "Y", color='black', ha='center', va='center')
    ax.text(0, 0, z_max / 2, "Z", color='black', ha='center', va='center')

    # 设置坐标轴范围
    ax.set_xlim([-x_max / 2, x_max / 2])
    ax.set_ylim([0, y_max])
    ax.set_zlim([-z_max / 2, z_max / 2])

    # 椭圆的半径设置：长轴为 x_max / 2，短轴为 z_max / 2
    y_values = np.linspace(0, y_max, num_ellipses)  # 等距的 y 轴坐标

    for y_val in y_values:
        theta = np.linspace(0, 2 * np.pi, 100)
        x_ellipse = (x_max / 2) * np.cos(theta)  # 长轴沿 x 轴
        z_ellipse = (z_max / 2) * np.sin(theta)  # 短轴沿 z 轴
        y_ellipse = np.full_like(x_ellipse, y_val)  # 椭圆沿 y 轴等距排列
        ax.plot(x_ellipse, y_ellipse, z_ellipse, color='g', lw=1)

    # 在原点处画一个蓝色小圆
    ax.scatter(0, 0, 0, color='b', s=50)

    # 设置标题和图例
    ax.set_title(f"Detected Targets: {len(cluster_coordinates)}")
    ax.legend()

    # 显示绘制结果
    plt.show()


def cluster_beam_energy_dbscan(beam_energy_3d, eps=5, min_samples=5, energy_threshold=0.995):
    """
    使用 DBSCAN 对 beam_energy_3d 进行聚类，筛选能量高于阈值的点
    :param beam_energy_3d: 输入的三维能量矩阵 [num_beams_horizontal, num_beams_vertical, num_samples]
    :param eps: 邻域半径参数
    :param min_samples: 簇中最小点数
    :param energy_threshold: 能量阈值，高于该阈值的点进行聚类
    :return: 聚类结果的标签，形状为 [num_beams_horizontal, num_beams_vertical, num_samples]
    """
    # 使用能量阈值筛选出高能量点
    high_energy_indices = np.where(beam_energy_3d >= energy_threshold * np.max(beam_energy_3d))
    high_energy_points = np.array(list(zip(high_energy_indices[0], high_energy_indices[1], high_energy_indices[2])))

    if len(high_energy_points) == 0:
        return np.zeros(beam_energy_3d.shape)  # 如果没有高能量点，返回全零矩阵

    # 使用 DBSCAN 对高能量点进行聚类
    dbscan = DBSCAN(eps=eps, min_samples=min_samples)
    labels = dbscan.fit_predict(high_energy_points)

    # 创建与原矩阵同样大小的标签矩阵，未聚类的点填充为 -1
    labels_3d = -1 * np.ones(beam_energy_3d.shape, dtype=int)
    for i, (x, y, z) in enumerate(high_energy_points):
        labels_3d[x, y, z] = labels[i]

    return labels_3d

def position(raw_data, mti_alpha=0.1,
              num_beams=27, max_angle=45,
              eps=5, min_samples=5,
              energy_threshold=0.995, position_threshold=0.5,
              plot=True):

    target_clusters = {}  # 存储不同目标的坐标

    # 获取天线数量和其他参数
    first_frame = raw_data.get_next_frame()
    num_ant = first_frame.data.shape[0]
    num_samples = first_frame.data.shape[2]
    num_chirps_per_frame = first_frame.data.shape[1]

    # 创建 DopplerAlgo 实例
    range_doppler_algo = DopplerAlgo(
        num_samples=num_samples,
        num_chirps_per_frame=num_chirps_per_frame,
        num_ant=num_ant,
        algo_type="doppler",
        mti_alpha=mti_alpha,
        mti=True)

    # 创建 DigitalBeamForming 实例
    dbf = DigitalBeamForming(num_antennas=2, num_beams=num_beams, max_angle_degrees=max_angle)

    start_frame = 50
    end_frame = raw_data.total_frames - 5

    for frame_idx in range(start_frame, end_frame):
        frame_data = raw_data.get_next_frame().data  # [num_ant, num_chirps, num_samples] 实

        # 对每个天线的数据进行处理
        fft_data_list = []
        for ant_idx in range(num_ant):
            single_ant_data = frame_data[ant_idx, :, :]  # [num_chirps, num_samples] 实
            range_fft = range_doppler_algo.compute_doppler_map(single_ant_data, i_ant=ant_idx)  # [num_samples, 2 * num_chirps] 复
            fft_data_list.append(range_fft)

        fft_data_combined = np.stack(fft_data_list, axis=-1)  # [num_samples, 2 * num_chirps, num_ant] 复

        # 处理水平角度的波束成形（使用天线 0 和 2）
        fft_data_horizontal = fft_data_combined[:, :, [2, 0]]  # 提取天线 0 和 2 的 FFT 数据
        result_horizontal = dbf.run(fft_data_horizontal)  # [num_samples, 2 * num_chirps, num_beams] 复
        beam_range_energy_horizontal = np.zeros((num_samples, num_beams))  # [num_samples, num_beams] 实
        for i_beam in range(num_beams):
            doppler_i = result_horizontal[:, :, i_beam]  # [num_samples, 2 * num_chirps] 复
            beam_range_energy_horizontal[:, i_beam] += np.linalg.norm(doppler_i, axis=1) / np.sqrt(num_beams)  # [num_samples] 实
        # 处理垂直角度的波束成形（使用天线 1 和 2）
        fft_data_vertical = fft_data_combined[:, :, [1, 2]]  # 提取天线 1 和 2 的 FFT 数据
        result_vertical = dbf.run(fft_data_vertical)  # [num_samples, 2 * num_chirps, num_beams_vertical] 复
        beam_range_energy_vertical = np.zeros((num_samples, num_beams))  # [num_samples, num_beams_vertical] 实
        for i_beam in range(num_beams):
            doppler_i = result_vertical[:, :, i_beam]  # [num_samples, 2 * num_chirps] 复
            beam_range_energy_vertical[:, i_beam] += np.linalg.norm(doppler_i, axis=1) / np.sqrt(num_beams)  # [num_samples] 实
        # **水平和垂直波束能量融合成三维能量矩阵** (num_beams_horizontal, num_beams_vertical, num_samples)
        beam_energy_3d = np.zeros((num_beams, num_beams, num_samples))  # [num_beams, num_beams, num_samples] 实
        for sample_idx in range(num_samples):
            beam_energy_3d[:, :, sample_idx] = np.outer(
                beam_range_energy_horizontal[sample_idx],  # [num_beams_horizontal]
                beam_range_energy_vertical[sample_idx]  # [num_beams_vertical]
            )  # 结果是 [num_beams_horizontal, num_beams_vertical] 的二维矩阵

        # 统一归一化
        max_energy_3d = np.max(beam_energy_3d)  # [实数]
        scale = 150
        beam_energy_3d = scale * (beam_energy_3d / max_energy_3d)  # [num_beams_horizontal, num_beams_vertical, num_samples] 实

        # 调用 DBSCAN 聚类算法对 beam_energy_3d 进行聚类
        labels_3d = cluster_beam_energy_dbscan(beam_energy_3d, eps=eps, min_samples=min_samples, energy_threshold=energy_threshold)
        # print(np.unique(beam_energy_3d))

        # 根据聚类标签区分目标
        unique_labels = np.unique(labels_3d)
        for label in unique_labels:
            if label == -1:
                continue  # 跳过噪声点
            label_indices = np.where(labels_3d == label)
            coordinates = np.column_stack((label_indices[0], label_indices[2], label_indices[1]))

            # 使用 Counter 计算频次，并根据 position_threshold 选择点
            peak_idx_tuples = [tuple(coord) for coord in coordinates]
            counter = Counter(peak_idx_tuples)
            total_count = sum(counter.values())
            cumulative_count = 0
            selected_coordinates = []

            for coord, count in counter.most_common():
                selected_coordinates.append(coord)
                cumulative_count += count
                if cumulative_count / total_count >= (1-position_threshold):
                    break
            # 将符合条件的坐标存入 target_clusters
            target_clusters[label] = selected_coordinates

    if plot:
        # 计算 y 轴一个 bin 实际距离
        y_dis = constants.c / (4 * abs(raw_data.header.end_frequency_Hz - raw_data.header.start_frequency_Hz))
        # 计算 x, y, z 轴的最大长度
        y_max = y_dis * num_samples  # y 轴的最大范围
        x_max = np.tan(np.radians(max_angle)) * y_max  # x 轴的最大范围
        z_max = np.tan(np.radians(max_angle)) * y_max  # z 轴的最大范围

        # 计算 x 轴一个 bin 实际距离
        x_dis = x_max / num_beams
        # 计算 z 轴一个 bin 实际距离
        z_dis = z_max / num_beams
        cluster_coordinates = {}
        for label, coordinates in target_clusters.items():
            max_energy_coordinates_list = []
            for i in range(len(coordinates)):
                x_coord = x_dis * (coordinates[i][0] - (num_beams/2))
                y_coord = y_dis * coordinates[i][1]
                z_coord = z_dis * (coordinates[i][2] - (num_beams/2))
                max_energy_coordinates = (x_coord, y_coord, z_coord)
                max_energy_coordinates_list.append(max_energy_coordinates)

            cluster_coordinates[label] = max_energy_coordinates_list
            # print(f"label: {label};Actual coordinates: {max_energy_coordinates_list}")
        # 绘制所有簇的坐标

        # print(f"x_dis: {x_dis}; y_dix: {y_dis}; z_dis: {z_dis}")
        # print(f"x_max: {x_max}; y_max: {y_max}; z_max: {z_max}")

        # 调用绘图函数并传入最大长度
        plot_max_energy_coordinates(cluster_coordinates, x_max, y_max, z_max)

    return target_clusters


def test():

    file_path = r"D:\PycharmProject\RADAR\HuayiHealthRadar_VSM\data\output\raw\20241107102653.dat"
    raw_data = RawParse(file_path)
    print(f"Frame Rate: {raw_data.header.frame_rate}  ;Chirp Num Per Frame: {raw_data.header.num_chirps_per_frame}  ;Sample Num Per Chirp: {raw_data.header.num_samples_per_chirp}")
    print(f"Range Resolution: {((constants.c / (2 * (raw_data.header.end_frequency_Hz - raw_data.header.start_frequency_Hz))) * 1000):.2f} mm  ;Max Range: {(((constants.c / (2 * (raw_data.header.end_frequency_Hz - raw_data.header.start_frequency_Hz))) / 2) * raw_data.header.num_samples_per_chirp):.2f} m")

    # 展示空间定位图
    target_clusters = position(raw_data=raw_data, mti_alpha=0.1,
                                num_beams=27, max_angle=45,
                                eps=5, min_samples=5, energy_threshold=0.995, position_threshold=0.1, plot=True)
    print(target_clusters)
if __name__ == "__main__":
    test()
