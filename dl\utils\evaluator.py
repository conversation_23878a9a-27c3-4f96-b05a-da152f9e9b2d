"""
模型评估器
"""
import torch
import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import classification_report, confusion_matrix
import seaborn as sns


class Evaluator:
    """模型评估器类"""
    
    def __init__(self, model, device):
        self.model = model
        self.device = device
        
    def evaluate(self, test_loader, class_names=None):
        """评估模型性能"""
        self.model.eval()
        
        all_predictions = []
        all_targets = []
        all_probabilities = []
        
        with torch.no_grad():
            for data, target in test_loader:
                data, target = data.to(self.device), target.to(self.device)
                output = self.model(data)
                
                # 获取预测结果
                probabilities = torch.softmax(output, dim=1)
                _, predicted = torch.max(output, 1)
                
                all_predictions.extend(predicted.cpu().numpy())
                all_targets.extend(target.cpu().numpy())
                all_probabilities.extend(probabilities.cpu().numpy())
        
        # 计算准确率
        accuracy = np.mean(np.array(all_predictions) == np.array(all_targets))
        
        print(f"测试准确率: {accuracy:.4f} ({accuracy*100:.2f}%)")
        
        # 生成分类报告
        if class_names is None:
            class_names = [f"Class_{i}" for i in range(len(set(all_targets)))]
        
        report = classification_report(all_targets, all_predictions, 
                                     target_names=class_names, 
                                     output_dict=True)
        
        print("\n分类报告:")
        print(classification_report(all_targets, all_predictions, 
                                  target_names=class_names))
        
        return {
            'accuracy': accuracy,
            'predictions': all_predictions,
            'targets': all_targets,
            'probabilities': all_probabilities,
            'classification_report': report
        }
    
    def plot_confusion_matrix(self, targets, predictions, class_names=None, 
                            save_path=None):
        """绘制混淆矩阵"""
        cm = confusion_matrix(targets, predictions)
        
        plt.figure(figsize=(10, 8))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=class_names, yticklabels=class_names)
        plt.title('混淆矩阵')
        plt.xlabel('预测标签')
        plt.ylabel('真实标签')
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_training_history(self, history, save_path=None):
        """绘制训练历史"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))
        
        # 损失曲线
        ax1.plot(history['loss'], label='训练损失')
        ax1.plot(history['val_loss'], label='验证损失')
        ax1.set_title('模型损失')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('损失')
        ax1.legend()
        ax1.grid(True)
        
        # 准确率曲线
        ax2.plot(history['accuracy'], label='训练准确率')
        ax2.plot(history['val_accuracy'], label='验证准确率')
        ax2.set_title('模型准确率')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('准确率 (%)')
        ax2.legend()
        ax2.grid(True)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
    
    def predict_single_image(self, image_tensor, class_names=None):
        """预测单张图像"""
        self.model.eval()
        
        with torch.no_grad():
            image_tensor = image_tensor.unsqueeze(0).to(self.device)
            output = self.model(image_tensor)
            probabilities = torch.softmax(output, dim=1)
            predicted_class = torch.argmax(probabilities, dim=1).item()
            confidence = probabilities[0][predicted_class].item()
        
        if class_names:
            predicted_label = class_names[predicted_class]
        else:
            predicted_label = f"Class_{predicted_class}"
        
        return {
            'predicted_class': predicted_class,
            'predicted_label': predicted_label,
            'confidence': confidence,
            'probabilities': probabilities[0].cpu().numpy()
        }
    
    def get_model_summary(self):
        """获取模型摘要信息"""
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        
        print(f"模型总参数数量: {total_params:,}")
        print(f"可训练参数数量: {trainable_params:,}")
        print(f"模型大小: {total_params * 4 / 1024 / 1024:.2f} MB")
        
        return {
            'total_params': total_params,
            'trainable_params': trainable_params,
            'model_size_mb': total_params * 4 / 1024 / 1024
        }
